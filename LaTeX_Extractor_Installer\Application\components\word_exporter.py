from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import xml.etree.ElementTree as ET
import re

# Import the new Linear transformer for enhanced Word compatibility
try:
    from .latex_to_linear_transformer import LaTeXToLinearTransformer
    LINEAR_TRANSFORMER_AVAILABLE = True
except ImportError:
    try:
        from latex_to_linear_transformer import LaTeXToLinearTransformer
        LINEAR_TRANSFORMER_AVAILABLE = True
    except ImportError:
        LINEAR_TRANSFORMER_AVAILABLE = False
        LaTeXToLinearTransformer = None

# Import multi-line equation parser
try:
    from .multiline_equation_parser import MultilineEquationParser
    MULTILINE_PARSER_AVAILABLE = True
except ImportError:
    try:
        from multiline_equation_parser import MultilineEquationParser
        MULTILINE_PARSER_AVAILABLE = True
    except ImportError:
        MULTILINE_PARSER_AVAILABLE = False
        MultilineEquationParser = None

class EnhancedWordExporter:
    def __init__(self, settings=None):
        self.settings = settings or {
            'show_page_refs': True,
            'inline_equations': False,
            'equation_numbering': True,
            'font_size': 12
        }

        # Initialize the Linear transformer for enhanced Word compatibility
        if LINEAR_TRANSFORMER_AVAILABLE:
            try:
                transformer_settings = {
                    'minimal_spacing': self.settings.get('minimal_spacing', True),
                    'avoid_extra_brackets': self.settings.get('avoid_extra_brackets', True),
                    'use_unicode_symbols': self.settings.get('use_unicode_symbols', True),
                    'word_compatibility': True,
                    'debug_mode': self.settings.get('debug_files', False)
                }
                self.linear_transformer = LaTeXToLinearTransformer(transformer_settings)
                print("✅ Word Exporter: Linear transformer initialized for enhanced compatibility")
            except Exception as e:
                print(f"⚠️  Word Exporter: Linear transformer initialization failed: {e}")
                self.linear_transformer = None
        else:
            print("⚠️  Word Exporter: Linear transformer not available - using fallback methods")
            self.linear_transformer = None

        # Initialize multi-line equation parser
        if MULTILINE_PARSER_AVAILABLE:
            try:
                self.multiline_parser = MultilineEquationParser()
                print("✅ Word Exporter: Multi-line equation parser initialized")
            except Exception as e:
                print(f"⚠️  Word Exporter: Multi-line parser initialization failed: {e}")
                self.multiline_parser = None
        else:
            print("⚠️  Word Exporter: Multi-line equation parser not available")
            self.multiline_parser = None
        
    def create_document(self, equations, filename):
        """Create Word document with proper OMML equations"""
        doc = Document()
        
        # Set document properties
        doc.core_properties.title = "Mathematical Equations"
        doc.core_properties.author = "MathCapture Studio"
        
        # Add title
        title = doc.add_heading('Mathematical Equations', 0)
        title.alignment = 1  # Center alignment
        
        # Process each equation
        for i, equation in enumerate(equations):
            self.add_equation_to_document(doc, equation, i + 1)
            
        doc.save(filename)
        
    def add_equation_to_document(self, doc, equation_data, number):
        """Add a single equation to the document"""
        # Handle both old and new equation data formats
        if hasattr(equation_data, 'latex_text'):
            latex_text = equation_data.latex_text
            source = getattr(equation_data, 'filename', 'Unknown')
        else:
            latex_text = equation_data.get('latex', '')
            source = equation_data.get('source', 'Unknown')

        # Add equation header
        if self.settings.get('show_page_refs', True):
            header_text = f"Equation {number} (from {source})"
        else:
            header_text = f"Equation {number}"

        header_para = doc.add_paragraph()
        header_run = header_para.add_run(header_text)
        header_run.bold = True

        # Add equation
        if self.settings.get('inline_equations', False):
            self.add_inline_equation(doc, latex_text)
        else:
            self.add_block_equation(doc, latex_text)

        # Add spacing
        doc.add_paragraph()
        
    def add_block_equation(self, doc, latex_text):
        """Add equation as a centered block"""
        para = doc.add_paragraph()
        para.alignment = 1  # Center alignment
        
        # Convert LaTeX to OMML
        omml_xml = self.latex_to_omml(latex_text)
        
        try:
            # Parse and insert OMML
            math_element = parse_xml(omml_xml)
            para._element.append(math_element)
        except:
            # Fallback to plain text
            para.add_run(latex_text)
            
    def add_inline_equation(self, doc, latex_text):
        """Add equation inline with text"""
        para = doc.add_paragraph()
        para.add_run("Equation: ")
        
        # Convert LaTeX to OMML
        omml_xml = self.latex_to_omml(latex_text)
        
        try:
            # Parse and insert OMML
            math_element = parse_xml(omml_xml)
            para._element.append(math_element)
        except:
            # Fallback to plain text
            para.add_run(latex_text)
            
    def latex_to_omml(self, latex_text):
        """Enhanced LaTeX to OMML converter with Linear transformer support"""
        try:
            # Try using the new Linear transformer first for better Word compatibility
            if self.linear_transformer:
                try:
                    # Use the Linear transformer for enhanced conversion
                    linear_format = self.linear_transformer.transform_latex_to_linear(latex_text)

                    # Validate the Linear format
                    validation = self.linear_transformer.validate_linear_format(linear_format)

                    if validation['is_valid']:
                        # Convert Linear format back to OMML
                        omml_xml = self.linear_transformer.linear_to_omml(linear_format)
                        print(f"✅ Used Linear transformer for: {latex_text[:50]}...")
                        return omml_xml
                    else:
                        print(f"⚠️  Linear format validation failed, using fallback for: {latex_text[:50]}...")

                except Exception as e:
                    print(f"⚠️  Linear transformer failed: {e}, using fallback")

            # Fallback to original enhanced method
            # Clean and prepare LaTeX text
            latex_text = self.prepare_latex_text(latex_text)

            # Convert to OMML using enhanced parser
            omml_content = self.enhanced_latex_to_omml(latex_text)

            # Wrap in proper OMML structure
            omml_xml = f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                {omml_content}
            </m:oMath>'''

            return omml_xml

        except Exception as e:
            print(f"OMML conversion error: {e}")
            # Final fallback to simple text
            return f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                <m:r><m:t>{latex_text}</m:t></m:r>
            </m:oMath>'''
        
    def parse_latex_to_omml(self, latex_text, parent, ns):
        """Parse LaTeX and convert to OMML elements"""
        # This is a simplified parser - a full implementation would
        # need a proper LaTeX parser
        
        # Handle common LaTeX constructs
        if '\\frac' in latex_text:
            self.handle_fraction(latex_text, parent, ns)
        elif '^' in latex_text:
            self.handle_superscript(latex_text, parent, ns)
        elif '_' in latex_text:
            self.handle_subscript(latex_text, parent, ns)
        elif '\\sqrt' in latex_text:
            self.handle_square_root(latex_text, parent, ns)
        else:
            # Simple text run
            r_elem = ET.SubElement(parent, f"{{{ns}}}r")
            t_elem = ET.SubElement(r_elem, f"{{{ns}}}t")
            t_elem.text = latex_text
            
    def handle_fraction(self, latex_text, parent, ns):
        """Handle \frac{numerator}{denominator}"""
        import re
        
        frac_pattern = r'\\frac\{([^}]+)\}\{([^}]+)\}'
        match = re.search(frac_pattern, latex_text)
        
        if match:
            numerator = match.group(1)
            denominator = match.group(2)
            
            # Create fraction element
            f_elem = ET.SubElement(parent, f"{{{ns}}}f")
            
            # Numerator
            num_elem = ET.SubElement(f_elem, f"{{{ns}}}num")
            self.parse_latex_to_omml(numerator, num_elem, ns)
            
            # Denominator
            den_elem = ET.SubElement(f_elem, f"{{{ns}}}den")
            self.parse_latex_to_omml(denominator, den_elem, ns)
            
    def handle_superscript(self, latex_text, parent, ns):
        """Handle superscripts x^{n}"""
        import re
        
        sup_pattern = r'([^\\^]+)\^\{([^}]+)\}'
        match = re.search(sup_pattern, latex_text)
        
        if match:
            base = match.group(1)
            superscript = match.group(2)
            
            # Create superscript element
            ssup_elem = ET.SubElement(parent, f"{{{ns}}}sSup")
            
            # Base
            e_elem = ET.SubElement(ssup_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(base, e_elem, ns)
            
            # Superscript
            sup_elem = ET.SubElement(ssup_elem, f"{{{ns}}}sup")
            self.parse_latex_to_omml(superscript, sup_elem, ns)
            
    def handle_subscript(self, latex_text, parent, ns):
        """Handle subscripts x_{n}"""
        import re
        
        sub_pattern = r'([^\\^_]+)_\{([^}]+)\}'
        match = re.search(sub_pattern, latex_text)
        
        if match:
            base = match.group(1)
            subscript = match.group(2)
            
            # Create subscript element
            ssub_elem = ET.SubElement(parent, f"{{{ns}}}sSub")
            
            # Base
            e_elem = ET.SubElement(ssub_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(base, e_elem, ns)
            
            # Subscript
            sub_elem = ET.SubElement(ssub_elem, f"{{{ns}}}sub")
            self.parse_latex_to_omml(subscript, sub_elem, ns)
            
    def handle_square_root(self, latex_text, parent, ns):
        r"""Handle \sqrt{expression}"""
        import re
        
        sqrt_pattern = r'\\sqrt\{([^}]+)\}'
        match = re.search(sqrt_pattern, latex_text)
        
        if match:
            expression = match.group(1)
            
            # Create radical element
            rad_elem = ET.SubElement(parent, f"{{{ns}}}rad")
            
            # Expression under radical
            e_elem = ET.SubElement(rad_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(expression, e_elem, ns)

    def prepare_latex_text(self, latex_text):
        """Clean and prepare LaTeX text for OMML conversion"""
        import re

        # Remove common LaTeX delimiters
        latex_text = latex_text.strip()
        latex_text = latex_text.replace('$', '').replace('\\[', '').replace('\\]', '')
        latex_text = latex_text.replace('\\(', '').replace('\\)', '')

        # Handle \mathrm{} commands - convert to plain text
        latex_text = re.sub(r'\\mathrm\{([^}]+)\}', r'\1', latex_text)
        latex_text = re.sub(r'\\text\{([^}]+)\}', r'\1', latex_text)
        latex_text = re.sub(r'\\textrm\{([^}]+)\}', r'\1', latex_text)

        # Handle sizing commands - remove them as Word handles sizing automatically
        latex_text = re.sub(r'\\left\b', '', latex_text)
        latex_text = re.sub(r'\\right\b', '', latex_text)
        latex_text = re.sub(r'\\big[glr]?\b', '', latex_text)
        latex_text = re.sub(r'\\Big[glr]?\b', '', latex_text)
        latex_text = re.sub(r'\\bigg[glr]?\b', '', latex_text)
        latex_text = re.sub(r'\\Bigg[glr]?\b', '', latex_text)

        # Handle spacing and dots commands
        latex_text = re.sub(r'\\cdots\b', '⋯', latex_text)  # Centered dots
        latex_text = re.sub(r'\\ldots\b', '…', latex_text)  # Lower dots
        latex_text = re.sub(r'\\vdots\b', '⋮', latex_text)  # Vertical dots
        latex_text = re.sub(r'\\ddots\b', '⋱', latex_text)  # Diagonal dots

        # Handle spacing commands
        latex_text = re.sub(r'\\,', ' ', latex_text)        # Thin space
        latex_text = re.sub(r'\\:', ' ', latex_text)        # Medium space
        latex_text = re.sub(r'\\;', ' ', latex_text)        # Thick space
        latex_text = re.sub(r'\\quad\b', '  ', latex_text)  # Quad space
        latex_text = re.sub(r'\\qquad\b', '    ', latex_text)  # Double quad space

        # Handle additional problematic commands
        latex_text = re.sub(r'\\equiv\b', '≡', latex_text)   # Equivalence symbol
        latex_text = re.sub(r'\\approx\b', '≈', latex_text)  # Approximately equal
        latex_text = re.sub(r'\\cong\b', '≅', latex_text)    # Congruent
        latex_text = re.sub(r'\\sim\b', '∼', latex_text)     # Similar

        # Handle bar/overline commands - convert to simple notation
        latex_text = re.sub(r'\\bar\{([^}]+)\}', r'\1̄', latex_text)  # Add combining overline
        latex_text = re.sub(r'\\overline\{([^}]+)\}', r'\1̄', latex_text)

        # Handle mathcal - convert to regular text (Word will style appropriately)
        latex_text = re.sub(r'\\mathcal\{([^}]+)\}', r'\1', latex_text)
        latex_text = re.sub(r'\\mathbb\{([^}]+)\}', r'\1', latex_text)
        latex_text = re.sub(r'\\mathbf\{([^}]+)\}', r'\1', latex_text)
        latex_text = re.sub(r'\\mathit\{([^}]+)\}', r'\1', latex_text)

        # Clean up extra spaces that might result from command removal
        latex_text = re.sub(r'\s+', ' ', latex_text)
        latex_text = latex_text.strip()

        # Handle array environments - convert to aligned equations
        latex_text = self.process_array_environments(latex_text)

        # Handle multi-line equations with proper alignment
        latex_text = self.process_alignment_structures(latex_text)

        return latex_text

    def process_array_environments(self, latex_text):
        """Process LaTeX array environments for better Word compatibility"""
        import re

        # Handle \begin{array} environments
        array_pattern = r'\\begin\{array\}\{[^}]*\}(.*?)\\end\{array\}'

        def replace_array(match):
            content = match.group(1)
            # Split by \\ for rows
            rows = content.split('\\\\')
            processed_rows = []

            for row in rows:
                if row.strip():
                    # Remove & alignment markers and clean up
                    clean_row = row.replace('&', ' = ').strip()
                    processed_rows.append(clean_row)

            # Join rows with line breaks for Word
            return ' \\\\ '.join(processed_rows)

        latex_text = re.sub(array_pattern, replace_array, latex_text, flags=re.DOTALL)

        return latex_text

    def process_alignment_structures(self, latex_text):
        """Process alignment structures for Word compatibility using multiline parser"""
        import re

        # Use multiline parser if available
        if self.multiline_parser and self.multiline_parser.is_multiline_equation(latex_text):
            # Process as complete multi-line equation
            processed_latex = self.multiline_parser.extract_complete_equation(latex_text)
            return processed_latex
        else:
            # Fallback to old method for single-line equations
            # Handle aligned environments
            latex_text = latex_text.replace('\\begin{aligned}', '').replace('\\end{aligned}', '')
            latex_text = latex_text.replace('\\begin{align}', '').replace('\\end{align}', '')
            latex_text = latex_text.replace('\\begin{align*}', '').replace('\\end{align*}', '')

            # Convert \\ line breaks to proper spacing
            latex_text = re.sub(r'\\\\', r' \\\\ ', latex_text)

            # Handle alignment markers (&) - convert to equals or appropriate spacing
            latex_text = re.sub(r'\s*&\s*=\s*', ' = ', latex_text)  # &= becomes =
            latex_text = re.sub(r'\s*&\s*', ' ', latex_text)  # & becomes space

            return latex_text

    def enhanced_latex_to_omml(self, latex_text):
        """Enhanced LaTeX to OMML conversion with proper nesting support"""
        if not latex_text or not latex_text.strip():
            return '<m:r><m:t></m:t></m:r>'

        # Check for multi-line equations
        if '\\\\' in latex_text:
            return self.handle_multiline_equation(latex_text)

        # Parse the LaTeX expression into tokens
        tokens = self.tokenize_latex(latex_text)

        # Convert tokens to OMML
        omml_elements = self.tokens_to_omml(tokens)

        # Combine elements
        if len(omml_elements) == 1:
            return omml_elements[0]
        else:
            # Multiple elements - wrap in a run
            combined = ""
            for element in omml_elements:
                combined += element
            return combined

    def handle_multiline_equation(self, latex_text):
        """Handle multi-line equations with proper alignment using multiline parser"""
        import re

        # Use multiline parser if available
        if self.multiline_parser and self.multiline_parser.is_multiline_equation(latex_text):
            # Get logical parts from parser
            parts = self.multiline_parser.split_into_logical_parts(latex_text)

            line_omml = []
            for i, part in enumerate(parts):
                part_content = part['content']

                # Handle alignment markers
                if '&' in part_content:
                    # Split by alignment marker
                    align_parts = part_content.split('&')
                    if len(align_parts) == 2:
                        left_part = align_parts[0].strip()
                        right_part = align_parts[1].strip()

                        # Create aligned equation
                        left_omml = self.enhanced_latex_to_omml(left_part) if left_part else ''
                        right_omml = self.enhanced_latex_to_omml(right_part) if right_part else ''

                        # Combine with alignment
                        line_omml.append(f'{left_omml}<m:r><m:t> </m:t></m:r>{right_omml}')
                    else:
                        # Multiple alignment points - join with spaces
                        part_omml = []
                        for align_part in align_parts:
                            align_part = align_part.strip()
                            if align_part:
                                part_omml.append(self.enhanced_latex_to_omml(align_part))
                        line_omml.append('<m:r><m:t> </m:t></m:r>'.join(part_omml))
                else:
                    # No alignment - process as regular line
                    line_omml.append(self.enhanced_latex_to_omml(part_content))

                # Add line break between parts (except for last part)
                if i < len(parts) - 1:
                    line_omml.append('<m:r><m:br/></m:r>')

            return '<m:r><m:t></m:t></m:r>'.join(line_omml)

        else:
            # Fallback to old method
            # Split by line breaks
            lines = re.split(r'\\\\', latex_text)

            # Process each line
            line_omml = []
            for line in lines:
                line = line.strip()
                if line:
                    # Handle alignment markers
                    if '&' in line:
                        # Split by alignment marker
                        parts = line.split('&')
                        if len(parts) == 2:
                            left_part = parts[0].strip()
                            right_part = parts[1].strip()

                            # Create aligned equation
                            left_omml = self.enhanced_latex_to_omml(left_part) if left_part else ''
                            right_omml = self.enhanced_latex_to_omml(right_part) if right_part else ''

                            # Combine with alignment
                            line_omml.append(f'{left_omml}<m:r><m:t> </m:t></m:r>{right_omml}')
                    else:
                        # Multiple alignment points - join with spaces
                        part_omml = []
                        for part in parts:
                            part = part.strip()
                            if part:
                                part_omml.append(self.enhanced_latex_to_omml(part))
                        line_omml.append('<m:r><m:t> </m:t></m:r>'.join(part_omml))
                else:
                    # No alignment marker - process normally but avoid recursion
                    tokens = self.tokenize_latex(line)
                    omml_elements = self.tokens_to_omml(tokens)
                    if len(omml_elements) == 1:
                        line_omml.append(omml_elements[0])
                    else:
                        combined = ""
                        for element in omml_elements:
                            combined += element
                        line_omml.append(combined)

        # Join lines with line breaks
        if line_omml:
            return '<m:r><m:br/></m:r>'.join(line_omml)
        else:
            return '<m:r><m:t></m:t></m:r>'

    def tokenize_latex(self, latex_text):
        """Tokenize LaTeX text into manageable components"""
        tokens = []
        i = 0

        while i < len(latex_text):
            # Check for LaTeX commands
            if latex_text[i] == '\\':
                # Find the end of the command
                j = i + 1
                while j < len(latex_text) and latex_text[j].isalpha():
                    j += 1

                command = latex_text[i:j]

                # Handle commands with arguments
                if command in ['\\frac', '\\sqrt', '\\int', '\\sum', '\\lim', '\\mathrm', '\\text', '\\textrm']:
                    token, end_pos = self.parse_command_with_args(latex_text, i)
                    tokens.append(token)
                    i = end_pos
                else:
                    # Simple command
                    tokens.append({'type': 'command', 'value': command})
                    i = j

            # Check for superscripts and subscripts
            elif latex_text[i] in ['^', '_']:
                token, end_pos = self.parse_script(latex_text, i)
                tokens.append(token)
                i = end_pos

            # Regular text
            else:
                # Find the end of regular text
                j = i
                while j < len(latex_text) and latex_text[j] not in ['\\', '^', '_', '{', '}']:
                    j += 1

                if j > i:
                    tokens.append({'type': 'text', 'value': latex_text[i:j]})
                    i = j
                else:
                    i += 1  # Skip single character

        return tokens

    def parse_command_with_args(self, latex_text, start_pos):
        """Parse LaTeX commands that have arguments like \\frac{a}{b}"""
        i = start_pos

        # Find command name
        while i < len(latex_text) and latex_text[i] != '{' and not latex_text[i].isspace():
            i += 1

        command = latex_text[start_pos:i]

        # Skip whitespace
        while i < len(latex_text) and latex_text[i].isspace():
            i += 1

        # Parse arguments
        args = []
        while i < len(latex_text) and latex_text[i] == '{':
            arg, end_pos = self.parse_braced_content(latex_text, i)
            args.append(arg)
            i = end_pos

        return {'type': 'command_with_args', 'command': command, 'args': args}, i

    def parse_script(self, latex_text, start_pos):
        """Parse superscript (^) or subscript (_) expressions"""
        script_type = 'superscript' if latex_text[start_pos] == '^' else 'subscript'
        i = start_pos + 1

        # Skip whitespace
        while i < len(latex_text) and latex_text[i].isspace():
            i += 1

        # Parse the script content
        if i < len(latex_text) and latex_text[i] == '{':
            content, end_pos = self.parse_braced_content(latex_text, i)
        else:
            # Single character script
            content = latex_text[i] if i < len(latex_text) else ''
            end_pos = i + 1

        return {'type': script_type, 'content': content}, end_pos

    def parse_braced_content(self, latex_text, start_pos):
        """Parse content within braces, handling nested braces"""
        if start_pos >= len(latex_text) or latex_text[start_pos] != '{':
            return '', start_pos

        i = start_pos + 1
        brace_count = 1
        content_start = i

        while i < len(latex_text) and brace_count > 0:
            if latex_text[i] == '{':
                brace_count += 1
            elif latex_text[i] == '}':
                brace_count -= 1
            i += 1

        content = latex_text[content_start:i-1] if brace_count == 0 else latex_text[content_start:]
        return content, i

    def tokens_to_omml(self, tokens):
        """Convert parsed tokens to OMML elements"""
        omml_elements = []
        i = 0

        while i < len(tokens):
            token = tokens[i]

            if token['type'] == 'text':
                omml_elements.append(f'<m:r><m:t>{self.escape_xml(token["value"])}</m:t></m:r>')
                i += 1

            elif token['type'] == 'command':
                omml_elements.append(self.convert_simple_command(token['value']))
                i += 1

            elif token['type'] == 'command_with_args':
                omml_elements.append(self.convert_command_with_args(token))
                i += 1

            elif token['type'] in ['superscript', 'subscript']:
                # Look for base element
                if omml_elements:
                    # Combine with previous element
                    base_element = omml_elements.pop()
                    script_element = self.convert_script(token, base_element)
                    omml_elements.append(script_element)
                else:
                    # No base - treat as standalone
                    omml_elements.append(self.convert_script(token, '<m:r><m:t></m:t></m:r>'))
                i += 1

            else:
                i += 1  # Skip unknown tokens

        return omml_elements

    def convert_simple_command(self, command):
        """Convert simple LaTeX commands to OMML"""
        symbol_map = {
            # Greek letters
            '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ', '\\delta': 'δ',
            '\\epsilon': 'ε', '\\theta': 'θ', '\\lambda': 'λ', '\\mu': 'μ',
            '\\pi': 'π', '\\sigma': 'σ', '\\phi': 'φ', '\\omega': 'ω',
            '\\Gamma': 'Γ', '\\Delta': 'Δ', '\\Theta': 'Θ', '\\Lambda': 'Λ',
            '\\Pi': 'Π', '\\Sigma': 'Σ', '\\Phi': 'Φ', '\\Omega': 'Ω',

            # Mathematical symbols
            '\\infty': '∞', '\\pm': '±', '\\mp': '∓',
            '\\leq': '≤', '\\geq': '≥', '\\neq': '≠', '\\approx': '≈',
            '\\equiv': '≡', '\\cong': '≅', '\\sim': '∼', '\\simeq': '≃',
            '\\times': '×', '\\div': '÷', '\\cdot': '·',
            '\\sum': '∑', '\\prod': '∏', '\\int': '∫',
            '\\partial': '∂', '\\nabla': '∇',

            # Arrows
            '\\rightarrow': '→', '\\leftarrow': '←', '\\Rightarrow': '⇒',
            '\\Leftarrow': '⇐', '\\leftrightarrow': '↔', '\\Leftrightarrow': '⇔',

            # Functions
            '\\sin': 'sin', '\\cos': 'cos', '\\tan': 'tan',
            '\\log': 'log', '\\ln': 'ln', '\\exp': 'exp',
            '\\sec': 'sec', '\\csc': 'csc', '\\cot': 'cot',

            # Sets and logic
            '\\in': '∈', '\\notin': '∉', '\\subset': '⊂', '\\supset': '⊃',
            '\\cup': '∪', '\\cap': '∩', '\\emptyset': '∅',
            '\\forall': '∀', '\\exists': '∃',

            # Dots (fallback if not caught by preprocessing)
            '\\cdots': '⋯', '\\ldots': '…', '\\vdots': '⋮', '\\ddots': '⋱'
        }

        symbol = symbol_map.get(command, command.replace('\\', ''))
        return f'<m:r><m:t>{symbol}</m:t></m:r>'

    def convert_command_with_args(self, token):
        """Convert LaTeX commands with arguments to OMML"""
        command = token['command']
        args = token['args']

        if command == '\\frac' and len(args) >= 2:
            return self.create_fraction_omml(args[0], args[1])
        elif command == '\\sqrt' and len(args) >= 1:
            return self.create_sqrt_omml(args[0])
        elif command == '\\int' and len(args) >= 1:
            return self.create_integral_omml(args)
        elif command == '\\sum' and len(args) >= 1:
            return self.create_sum_omml(args)
        elif command == '\\lim' and len(args) >= 1:
            return self.create_limit_omml(args)
        elif command in ['\\mathrm', '\\text', '\\textrm'] and len(args) >= 1:
            # Convert text commands to plain text
            return f'<m:r><m:t>{self.escape_xml(args[0])}</m:t></m:r>'
        else:
            # Fallback
            return f'<m:r><m:t>{command}({", ".join(args)})</m:t></m:r>'

    def create_fraction_omml(self, numerator, denominator):
        """Create OMML for fractions"""
        # Clean and process numerator and denominator
        numerator = numerator.strip()
        denominator = denominator.strip()

        # Handle empty cases
        if not numerator:
            numerator = " "
        if not denominator:
            denominator = " "

        # Convert to OMML recursively
        num_omml = self.enhanced_latex_to_omml(numerator)
        den_omml = self.enhanced_latex_to_omml(denominator)

        # Ensure proper OMML structure
        if not num_omml or not num_omml.strip():
            num_omml = '<m:r><m:t> </m:t></m:r>'
        if not den_omml or not den_omml.strip():
            den_omml = '<m:r><m:t> </m:t></m:r>'

        return f'''<m:f>
            <m:fPr></m:fPr>
            <m:num>{num_omml}</m:num>
            <m:den>{den_omml}</m:den>
        </m:f>'''

    def create_sqrt_omml(self, expression):
        """Create OMML for square roots"""
        expr_omml = self.enhanced_latex_to_omml(expression)

        return f'''<m:rad>
            <m:radPr></m:radPr>
            <m:deg></m:deg>
            <m:e>{expr_omml}</m:e>
        </m:rad>'''

    def create_integral_omml(self, args):
        """Create OMML for integrals"""
        # Basic integral symbol
        integral_omml = '<m:r><m:t>∫</m:t></m:r>'

        # Add bounds if present
        if len(args) >= 2:
            # Integral with bounds
            lower_bound = self.enhanced_latex_to_omml(args[0])
            upper_bound = self.enhanced_latex_to_omml(args[1])

            integral_omml = f'''<m:sSubSup>
                <m:sSubSupPr></m:sSubSupPr>
                <m:e><m:r><m:t>∫</m:t></m:r></m:e>
                <m:sub>{lower_bound}</m:sub>
                <m:sup>{upper_bound}</m:sup>
            </m:sSubSup>'''

        return integral_omml

    def create_sum_omml(self, args):
        """Create OMML for summations"""
        # Basic sum symbol
        sum_omml = '<m:r><m:t>∑</m:t></m:r>'

        # Add bounds if present
        if len(args) >= 2:
            lower_bound = self.enhanced_latex_to_omml(args[0])
            upper_bound = self.enhanced_latex_to_omml(args[1])

            sum_omml = f'''<m:sSubSup>
                <m:sSubSupPr></m:sSubSupPr>
                <m:e><m:r><m:t>∑</m:t></m:r></m:e>
                <m:sub>{lower_bound}</m:sub>
                <m:sup>{upper_bound}</m:sup>
            </m:sSubSup>'''

        return sum_omml

    def create_limit_omml(self, args):
        """Create OMML for limits"""
        limit_expr = self.enhanced_latex_to_omml(args[0])

        return f'''<m:func>
            <m:funcPr></m:funcPr>
            <m:fName><m:r><m:t>lim</m:t></m:r></m:fName>
            <m:e>{limit_expr}</m:e>
        </m:func>'''

    def convert_script(self, token, base_element):
        """Convert superscript or subscript to OMML"""
        script_content = self.enhanced_latex_to_omml(token['content'])

        if token['type'] == 'superscript':
            return f'''<m:sSup>
                <m:sSupPr></m:sSupPr>
                <m:e>{base_element}</m:e>
                <m:sup>{script_content}</m:sup>
            </m:sSup>'''
        else:  # subscript
            return f'''<m:sSub>
                <m:sSubPr></m:sSubPr>
                <m:e>{base_element}</m:e>
                <m:sub>{script_content}</m:sub>
            </m:sSub>'''

    def escape_xml(self, text):
        """Escape XML special characters"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&apos;'))

    def create_document_with_equations(self, equations, filename):
        """Create a Word document with multiple equations"""
        doc = Document()

        # Set document properties
        doc.core_properties.title = "Mathematical Equations"
        doc.core_properties.author = "LaTeX Extractor"

        # Add title
        title = doc.add_heading('Mathematical Equations', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add each equation
        for i, equation_data in enumerate(equations):
            self.add_equation_to_document(doc, equation_data, i + 1)

        # Save document
        doc.save(filename)
        return filename

    def export_single_equation(self, latex_text, filename):
        """Export a single equation to a Word document"""
        equations = [{'latex': latex_text, 'source': 'Manual Entry'}]
        return self.create_document_with_equations(equations, filename)

# Compatibility alias for backward compatibility
WordExporter = EnhancedWordExporter