# Dependencies Directory

This directory should contain the following files for the installer:

## Required Files:
- `python-3.13.3-amd64.exe` - Python installer from D:\New folder\
- `tesseract-ocr-w64-setup-5.5.0.20241111.exe` - Tesseract OCR installer from D:\New folder\
- `OllamaSetup.exe` - Ollama installer from D:\New folder\
- `poppler-24.08.0.zip` - Poppler binaries (create zip from D:\poppler-windows\poppler-24.08.0)
- `requirements.txt` - Python package requirements (copied automatically)

## Instructions:
1. Copy the installer files from D:\New folder\ to this directory
2. Create poppler-24.08.0.zip from the Poppler installation
3. Run the installer build script
