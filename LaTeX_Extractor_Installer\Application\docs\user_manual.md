# MathCapture Studio User Manual

## Table of Contents
1. [Installation](#installation)
2. [Basic Usage](#basic-usage)
3. [Features](#features)
4. [Troubleshooting](#troubleshooting)

## Installation

### Prerequisites
- Python 3.8+
- Tesseract OCR installed on your system

### Steps
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run the application: `python main.py`

## Basic Usage

1. **Importing Files**
   - Click 'File > Import' or use the toolbar button
   - Select PDF or image files containing mathematical equations

2. **Selecting Equations**
   - Click and drag on the preview to select equation regions
   - Selected regions will be highlighted in red

3. **Processing Equations**
   - Click 'Process OCR' to extract text from selected region
   - Edit the LaTeX output if needed
   - Add equations to the queue with 'Add to Queue'

4. **Exporting to Word**
   - Click 'File > Export to Word'
   - Choose output location and filename
   - Equations will be formatted with proper mathematical notation

## Features

- **Multi-file Support**: Process equations from multiple PDFs/images
- **Equation Queue**: Manage equations before exporting
- **Customizable Settings**: Adjust OCR parameters and output formatting
- **Responsive UI**: Adapts to different screen sizes

## Troubleshooting

### Common Issues

**OCR not working**
- Ensure Tesseract OCR is installed and in your PATH
- Check the selected region contains clear text

**Export formatting issues**
- Verify you have write permissions to the output location
- Check that Microsoft Word is installed for preview functionality