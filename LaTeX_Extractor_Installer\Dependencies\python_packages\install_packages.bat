@echo off
echo Installing LaTeX Extractor Python packages...
echo This may take several minutes...

cd /d "%~dp0"

REM Install packages from wheels directory
python -m pip install --find-links wheels --no-index --force-reinstall -r ..
equirements.txt

REM Install specific packages if available
for %%f in (wheels\*.whl) do (
    echo Installing %%f...
    python -m pip install --force-reinstall "%%f"
)

echo Package installation completed!
pause
