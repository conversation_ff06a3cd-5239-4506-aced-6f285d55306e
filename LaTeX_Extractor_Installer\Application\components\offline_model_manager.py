#!/usr/bin/env python3
"""
LaTeX Extractor by Yark - Offline Model Manager
Handles downloading and caching of AI models for offline use
"""

import os
import sys
import requests
import json
import hashlib
from pathlib import Path
import time
import threading
from typing import Optional, Callable

class OfflineModelManager:
    """Manages AI models for offline functionality"""
    
    def __init__(self):
        self.models_dir = Path.home() / "AppData" / "Local" / "LaTeX Extractor" / "models"
        self.cache_dir = Path.home() / "AppData" / "Local" / "LaTeX Extractor" / "cache"
        self.config_file = self.models_dir / "models_config.json"
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Model definitions
        self.available_models = {
            "latex_ocr": {
                "name": "LaTeX-OCR Model",
                "description": "Converts mathematical images to LaTeX",
                "size_mb": 150,
                "required": True,
                "url": "https://huggingface.co/lukasb/pix2tex/resolve/main/pytorch_model.bin",
                "config_url": "https://huggingface.co/lukasb/pix2tex/resolve/main/config.json",
                "local_path": "latex_ocr/pytorch_model.bin",
                "config_path": "latex_ocr/config.json"
            },
            "ollama_llama3": {
                "name": "Llama 3 Model",
                "description": "AI model for LaTeX enhancement",
                "size_mb": 4000,
                "required": False,
                "url": None,  # Managed by Ollama
                "local_path": None,
                "ollama_model": "llama3"
            }
        }
        
        self.download_progress_callback = None
        self.is_downloading = False
    
    def log(self, message, level="INFO"):
        """Log manager activity"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def set_progress_callback(self, callback: Callable[[str, int], None]):
        """Set callback for download progress updates"""
        self.download_progress_callback = callback
    
    def get_model_status(self):
        """Get status of all models"""
        status = {}
        
        for model_id, model_info in self.available_models.items():
            if model_info.get("local_path"):
                model_path = self.models_dir / model_info["local_path"]
                status[model_id] = {
                    "available": model_path.exists(),
                    "size_mb": model_path.stat().st_size / 1024 / 1024 if model_path.exists() else 0,
                    "required": model_info["required"],
                    "description": model_info["description"]
                }
            elif model_info.get("ollama_model"):
                # Check Ollama model
                status[model_id] = {
                    "available": self.check_ollama_model(model_info["ollama_model"]),
                    "size_mb": model_info["size_mb"],
                    "required": model_info["required"],
                    "description": model_info["description"]
                }
        
        return status
    
    def check_ollama_model(self, model_name):
        """Check if Ollama model is available"""
        try:
            import subprocess
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            return model_name in result.stdout
        except:
            return False
    
    def download_file_with_progress(self, url, destination, description=""):
        """Download file with progress reporting"""
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            with open(destination, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = int((downloaded / total_size) * 100)
                            if self.download_progress_callback:
                                self.download_progress_callback(description, progress)
                            else:
                                print(f"\rDownloading {description}: {progress}%", end="", flush=True)
            
            print()  # New line after progress
            return True
            
        except Exception as e:
            self.log(f"Download failed: {e}", "ERROR")
            return False
    
    def download_latex_ocr_model(self):
        """Download LaTeX-OCR model"""
        model_info = self.available_models["latex_ocr"]
        
        self.log("Downloading LaTeX-OCR model...")
        
        # Download main model
        model_path = self.models_dir / model_info["local_path"]
        if not self.download_file_with_progress(
            model_info["url"], 
            model_path, 
            "LaTeX-OCR Model"
        ):
            return False
        
        # Download config
        config_path = self.models_dir / model_info["config_path"]
        if not self.download_file_with_progress(
            model_info["config_url"], 
            config_path, 
            "LaTeX-OCR Config"
        ):
            return False
        
        self.log("✅ LaTeX-OCR model downloaded successfully")
        return True
    
    def download_ollama_model(self):
        """Download Ollama Llama 3 model"""
        model_info = self.available_models["ollama_llama3"]
        
        self.log("Downloading Llama 3 model via Ollama...")
        
        try:
            import subprocess
            
            # Check if Ollama is available
            result = subprocess.run(['ollama', '--version'], capture_output=True)
            if result.returncode != 0:
                self.log("Ollama not available", "ERROR")
                return False
            
            # Pull the model
            self.log("Pulling Llama 3 model (this may take a while)...")
            result = subprocess.run(['ollama', 'pull', model_info["ollama_model"]], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("✅ Llama 3 model downloaded successfully")
                return True
            else:
                self.log(f"Ollama pull failed: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Ollama download error: {e}", "ERROR")
            return False
    
    def download_required_models(self):
        """Download all required models"""
        self.log("🚀 Starting required model downloads...")
        self.is_downloading = True
        
        success_count = 0
        total_required = sum(1 for model in self.available_models.values() if model["required"])
        
        for model_id, model_info in self.available_models.items():
            if not model_info["required"]:
                continue
            
            self.log(f"Processing {model_info['name']}...")
            
            if model_id == "latex_ocr":
                if self.download_latex_ocr_model():
                    success_count += 1
            elif model_id == "ollama_llama3":
                if self.download_ollama_model():
                    success_count += 1
        
        self.is_downloading = False
        
        if success_count == total_required:
            self.log("🎉 All required models downloaded successfully!")
            self.save_model_config()
            return True
        else:
            self.log(f"⚠️  Downloaded {success_count}/{total_required} required models", "WARNING")
            return False
    
    def download_optional_models(self):
        """Download optional models"""
        self.log("📦 Starting optional model downloads...")
        
        for model_id, model_info in self.available_models.items():
            if model_info["required"]:
                continue
            
            self.log(f"Processing optional {model_info['name']}...")
            
            if model_id == "ollama_llama3":
                self.download_ollama_model()
    
    def save_model_config(self):
        """Save model configuration"""
        config = {
            "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
            "models": self.get_model_status(),
            "version": "2.0.0"
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.log(f"✅ Model configuration saved: {self.config_file}")
            
        except Exception as e:
            self.log(f"❌ Failed to save model config: {e}", "ERROR")
    
    def load_model_config(self):
        """Load model configuration"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log(f"⚠️  Could not load model config: {e}", "WARNING")
        
        return None
    
    def check_models_available(self):
        """Check if required models are available for offline use"""
        status = self.get_model_status()
        
        required_available = all(
            status[model_id]["available"] 
            for model_id, model_info in self.available_models.items() 
            if model_info["required"]
        )
        
        return required_available, status
    
    def get_model_paths(self):
        """Get paths to downloaded models"""
        paths = {}
        
        for model_id, model_info in self.available_models.items():
            if model_info.get("local_path"):
                model_path = self.models_dir / model_info["local_path"]
                if model_path.exists():
                    paths[model_id] = str(model_path)
        
        return paths
    
    def cleanup_old_models(self):
        """Clean up old or corrupted model files"""
        self.log("🧹 Cleaning up old models...")
        
        try:
            # Remove any .tmp files
            for tmp_file in self.models_dir.glob("**/*.tmp"):
                tmp_file.unlink()
                self.log(f"Removed temporary file: {tmp_file.name}")
            
            # Remove empty directories
            for dir_path in self.models_dir.glob("**/"):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    dir_path.rmdir()
                    self.log(f"Removed empty directory: {dir_path.name}")
            
            self.log("✅ Model cleanup completed")
            
        except Exception as e:
            self.log(f"⚠️  Model cleanup warning: {e}", "WARNING")

def download_models_background(progress_callback=None):
    """Download models in background thread"""
    def download_thread():
        manager = OfflineModelManager()
        if progress_callback:
            manager.set_progress_callback(progress_callback)
        
        manager.download_required_models()
    
    thread = threading.Thread(target=download_thread, daemon=True)
    thread.start()
    return thread

def main():
    """Test the offline model manager"""
    print("LaTeX Extractor by Yark - Offline Model Manager Test")
    print("=" * 50)
    
    manager = OfflineModelManager()
    
    # Check current status
    print("\n📋 Current Model Status:")
    status = manager.get_model_status()
    for model_id, info in status.items():
        status_icon = "✅" if info["available"] else "❌"
        required_text = "(Required)" if info["required"] else "(Optional)"
        print(f"{status_icon} {model_id}: {info['description']} {required_text}")
        print(f"   Size: {info['size_mb']:.1f} MB")
    
    # Check if models are available
    available, _ = manager.check_models_available()
    print(f"\n🎯 Required models available: {'Yes' if available else 'No'}")
    
    if not available:
        print("\n💡 Run with --download to download required models")
        
        if len(sys.argv) > 1 and sys.argv[1] == "--download":
            manager.download_required_models()

if __name__ == "__main__":
    main()
