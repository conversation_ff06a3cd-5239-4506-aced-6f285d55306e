#!/usr/bin/env python3
"""
LaTeX Extractor by Yark - Path Configurator
Dynamic path detection and configuration for installed version
"""

import os
import sys
import json
import shutil
from pathlib import Path
import platform

class PathConfigurator:
    """Handles dynamic path detection for all dependencies"""
    
    def __init__(self):
        self.app_dir = Path(__file__).parent.parent
        self.config_file = self.app_dir / "config" / "install_config.json"
        self.paths = {}
        
        # Load configuration if available
        self.load_install_config()
        
        # Detect paths dynamically
        self.detect_all_paths()
    
    def load_install_config(self):
        """Load installation configuration if available"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # Extract dependency paths from config
                if 'dependencies' in config:
                    deps = config['dependencies']
                    self.paths.update({
                        'python_path': deps.get('python_path'),
                        'tesseract_path': deps.get('tesseract_path'),
                        'ollama_path': deps.get('ollama_path'),
                        'poppler_path': deps.get('poppler_path')
                    })
                    
                print(f"✅ Loaded install configuration from {self.config_file}")
                
        except Exception as e:
            print(f"⚠️  Could not load install config: {e}")
    
    def detect_python_path(self):
        """Detect Python executable path"""
        python_candidates = [
            # From install config
            self.paths.get('python_path'),
            
            # System Python
            shutil.which('python'),
            shutil.which('py'),
            
            # Common installation paths
            r'C:\Program Files\Python313\python.exe',
            r'C:\Program Files (x86)\Python313\python.exe',
            r'C:\Python313\python.exe',
            
            # Installer-specific path
            str(self.app_dir.parent / "Python" / "python.exe"),
            
            # Current Python executable
            sys.executable
        ]
        
        for candidate in python_candidates:
            if candidate and Path(candidate).exists():
                self.paths['python_path'] = str(candidate)
                print(f"✅ Found Python: {candidate}")
                return candidate
        
        print("⚠️  Python not found")
        return None
    
    def detect_tesseract_path(self):
        """Detect Tesseract OCR executable path"""
        tesseract_candidates = [
            # From install config
            self.paths.get('tesseract_path'),
            
            # System PATH
            shutil.which('tesseract'),
            
            # Common installation paths
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            
            # Installer-specific path
            str(self.app_dir.parent / "Tesseract-OCR" / "tesseract.exe"),
            
            # User-specific installation
            str(Path.home() / "AppData" / "Local" / "Tesseract-OCR" / "tesseract.exe")
        ]
        
        for candidate in tesseract_candidates:
            if candidate and Path(candidate).exists():
                self.paths['tesseract_path'] = str(candidate)
                print(f"✅ Found Tesseract: {candidate}")
                return candidate
        
        print("⚠️  Tesseract not found")
        return None
    
    def detect_poppler_path(self):
        """Detect Poppler binaries path with comprehensive search"""
        # Get the installation directory (where the application is installed)
        if hasattr(sys, '_MEIPASS'):
            # Running as PyInstaller executable
            install_base = Path(sys.executable).parent
        else:
            # Running as Python script
            install_base = self.app_dir.parent

        poppler_candidates = [
            # From install config
            self.paths.get('poppler_path'),

            # Installer-specific paths (multiple variations)
            str(install_base / "poppler" / "poppler-24.08.0" / "Library" / "bin"),
            str(install_base / "poppler" / "Library" / "bin"),
            str(install_base / "poppler" / "bin"),
            str(install_base / "Dependencies" / "poppler" / "poppler-24.08.0" / "Library" / "bin"),
            str(install_base / "Dependencies" / "poppler" / "Library" / "bin"),

            # Application directory relative paths
            str(self.app_dir / "poppler" / "poppler-24.08.0" / "Library" / "bin"),
            str(self.app_dir / "poppler" / "Library" / "bin"),
            str(self.app_dir / "poppler" / "bin"),

            # System-wide installation paths
            r'C:\Program Files\LaTeX Extractor by Yark\poppler\poppler-24.08.0\Library\bin',
            r'C:\Program Files\LaTeX Extractor by Yark\poppler\Library\bin',
            r'C:\Program Files (x86)\LaTeX Extractor by Yark\poppler\poppler-24.08.0\Library\bin',
            r'C:\Program Files (x86)\LaTeX Extractor by Yark\poppler\Library\bin',

            # Original development path (fallback)
            r"D:\poppler-windows\poppler-24.08.0\Library\bin",

            # Common installation paths
            r'C:\Program Files\poppler\bin',
            r'C:\Program Files (x86)\poppler\bin',
            r'C:\poppler\bin'
        ]

        for candidate in poppler_candidates:
            if candidate and Path(candidate).exists():
                # Verify poppler tools exist
                required_tools = ["pdftoppm.exe", "pdfinfo.exe", "pdfimages.exe"]
                tools_found = 0

                for tool in required_tools:
                    tool_path = Path(candidate) / tool
                    if tool_path.exists():
                        tools_found += 1

                if tools_found >= 1:  # At least one tool found
                    self.paths['poppler_path'] = str(candidate)
                    print(f"✅ Found Poppler: {candidate} ({tools_found}/{len(required_tools)} tools)")
                    return candidate

        print("⚠️  Poppler not found - PDF processing may not work")
        print("💡 Searched locations:")
        for candidate in poppler_candidates[:10]:  # Show first 10 locations
            if candidate:
                print(f"   - {candidate}")
        return None
    
    def detect_ollama_path(self):
        """Detect Ollama executable path"""
        ollama_candidates = [
            # From install config
            self.paths.get('ollama_path'),
            
            # System PATH
            shutil.which('ollama'),
            
            # Common installation paths
            str(Path(os.environ.get('LOCALAPPDATA', '')) / 'Programs' / 'Ollama' / 'ollama.exe'),
            str(Path(os.environ.get('PROGRAMFILES', '')) / 'Ollama' / 'ollama.exe'),
            r'C:\Program Files\Ollama\ollama.exe',
            r'C:\Program Files (x86)\Ollama\ollama.exe'
        ]
        
        for candidate in ollama_candidates:
            if candidate and Path(candidate).exists():
                self.paths['ollama_path'] = str(candidate)
                print(f"✅ Found Ollama: {candidate}")
                return candidate
        
        print("⚠️  Ollama not found")
        return None
    
    def detect_all_paths(self):
        """Detect all dependency paths"""
        print("🔍 Detecting dependency paths...")
        
        self.detect_python_path()
        self.detect_tesseract_path()
        self.detect_poppler_path()
        self.detect_ollama_path()
        
        # Set environment variables for pdf2image
        if self.paths.get('poppler_path'):
            os.environ['POPPLER_PATH'] = self.paths['poppler_path']
        
        # Configure pytesseract if available
        if self.paths.get('tesseract_path'):
            try:
                import pytesseract
                pytesseract.pytesseract.tesseract_cmd = self.paths['tesseract_path']
                print("✅ Configured pytesseract")
            except ImportError:
                print("⚠️  pytesseract not available")
    
    def get_poppler_path(self):
        """Get Poppler path for pdf2image"""
        return self.paths.get('poppler_path')
    
    def get_tesseract_path(self):
        """Get Tesseract path for pytesseract"""
        return self.paths.get('tesseract_path')
    
    def get_python_path(self):
        """Get Python executable path"""
        return self.paths.get('python_path')
    
    def get_ollama_path(self):
        """Get Ollama executable path"""
        return self.paths.get('ollama_path')
    
    def is_dependency_available(self, dependency):
        """Check if a specific dependency is available"""
        path_key = f"{dependency}_path"
        return path_key in self.paths and self.paths[path_key] is not None
    
    def get_all_paths(self):
        """Get all detected paths"""
        return self.paths.copy()
    
    def validate_paths(self):
        """Validate all detected paths"""
        print("🔍 Validating dependency paths...")
        
        validation_results = {}
        
        for dep_name, path in self.paths.items():
            if path and Path(path).exists():
                validation_results[dep_name] = True
                print(f"✅ {dep_name}: {path}")
            else:
                validation_results[dep_name] = False
                print(f"❌ {dep_name}: Not found or invalid")
        
        return validation_results
    
    def create_runtime_config(self):
        """Create runtime configuration file"""
        runtime_config = {
            "paths": self.paths,
            "validation": self.validate_paths(),
            "platform": platform.system(),
            "architecture": platform.machine(),
            "python_version": sys.version,
            "app_dir": str(self.app_dir)
        }
        
        runtime_config_file = self.app_dir / "config" / "runtime_config.json"
        
        try:
            with open(runtime_config_file, 'w', encoding='utf-8') as f:
                json.dump(runtime_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Runtime configuration saved: {runtime_config_file}")
            return runtime_config_file
            
        except Exception as e:
            print(f"❌ Failed to save runtime configuration: {e}")
            return None

# Global instance for easy access
_path_configurator = None

def get_path_configurator():
    """Get global path configurator instance"""
    global _path_configurator
    if _path_configurator is None:
        _path_configurator = PathConfigurator()
    return _path_configurator

def get_poppler_path():
    """Get Poppler path for pdf2image"""
    return get_path_configurator().get_poppler_path()

def get_tesseract_path():
    """Get Tesseract path for pytesseract"""
    return get_path_configurator().get_tesseract_path()

def configure_dependencies():
    """Configure all dependencies for the application"""
    configurator = get_path_configurator()
    
    # Configure pytesseract
    tesseract_path = configurator.get_tesseract_path()
    if tesseract_path:
        try:
            import pytesseract
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        except ImportError:
            pass
    
    # Set environment variables
    poppler_path = configurator.get_poppler_path()
    if poppler_path:
        os.environ['POPPLER_PATH'] = poppler_path
    
    return configurator

if __name__ == "__main__":
    # Test the path configurator
    print("LaTeX Extractor by Yark - Path Configurator Test")
    print("=" * 50)
    
    configurator = PathConfigurator()
    
    print("\n📋 Detected Paths:")
    for name, path in configurator.get_all_paths().items():
        status = "✅" if path else "❌"
        print(f"{status} {name}: {path or 'Not found'}")
    
    print("\n🔍 Validation Results:")
    validation = configurator.validate_paths()
    for name, valid in validation.items():
        status = "✅" if valid else "❌"
        print(f"{status} {name}: {'Valid' if valid else 'Invalid'}")
    
    # Create runtime config
    config_file = configurator.create_runtime_config()
    if config_file:
        print(f"\n📄 Runtime config: {config_file}")
    
    print("\n✅ Path configuration test completed!")
