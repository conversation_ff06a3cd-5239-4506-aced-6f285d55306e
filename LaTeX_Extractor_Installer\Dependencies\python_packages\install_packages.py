#!/usr/bin/env python3
"""
Offline package installer for LaTeX Extractor
"""

import os
import sys
import subprocess
from pathlib import Path

def install_packages():
    """Install packages from wheels directory"""
    print("Installing LaTeX Extractor Python packages...")
    
    wheels_dir = Path(__file__).parent / "wheels"
    requirements_file = Path(__file__).parent.parent / "requirements.txt"
    
    if not wheels_dir.exists():
        print("❌ Wheels directory not found")
        return False
    
    try:
        # Install from wheels with no internet
        cmd = [
            sys.executable, '-m', 'pip', 'install',
            '--find-links', str(wheels_dir),
            '--no-index',
            '--force-reinstall',
            '-r', str(requirements_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Packages installed successfully")
            return True
        else:
            print(f"❌ Installation failed: {result.stderr}")
            
            # Try installing individual wheels
            print("Trying individual wheel installation...")
            wheel_files = list(wheels_dir.glob("*.whl"))
            
            for wheel_file in wheel_files:
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', '--force-reinstall', str(wheel_file)], 
                                 capture_output=True, check=True)
                    print(f"✅ Installed {wheel_file.name}")
                except:
                    print(f"⚠️  Failed to install {wheel_file.name}")
            
            return True
            
    except Exception as e:
        print(f"❌ Installation error: {e}")
        return False

if __name__ == "__main__":
    success = install_packages()
    if success:
        print("\n✅ Package installation completed!")
    else:
        print("\n❌ Package installation failed!")
    
    input("Press Enter to exit...")
