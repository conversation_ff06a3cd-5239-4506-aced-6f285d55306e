#!/usr/bin/env python3
"""
Python-based offline package installer for LaTeX Extractor
"""

import os
import sys
import subprocess
from pathlib import Path

def install_packages():
    """Install packages from wheels directory"""
    script_dir = Path(__file__).parent
    wheels_dir = script_dir / "wheels"

    if not wheels_dir.exists():
        print("[ERROR] Wheels directory not found")
        return False

    packages = [
        "Pillow", "opencv-python", "numpy",
        "PyMuPDF", "pdf2image",
        "pytesseract", "pix2tex",
        "torch", "torchvision", "transformers", "accelerate", "protobuf",
        "python-docx", "lxml",
        "requests", "psutil", "typing-extensions"
    ]

    print("[INSTALL] Installing LaTeX Extractor dependencies...")

    for package in packages:
        print(f"[INSTALL] Installing {package}...")
        try:
            cmd = [
                sys.executable, "-m", "pip", "install",
                "--no-index", "--find-links", str(wheels_dir),
                package
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"[SUCCESS] {package} installed successfully")
            else:
                print(f"[WARNING] {package} installation warning: {result.stderr}")

        except Exception as e:
            print(f"[ERROR] Failed to install {package}: {e}")

    print("[COMPLETE] Package installation completed!")
    return True

if __name__ == "__main__":
    install_packages()
