#!/usr/bin/env python3
"""
Equation Preview Widget for LaTeX Extractor

This module provides an integrated equation preview widget that displays
mathematical equations without requiring Microsoft Word to be opened.
It uses the LaTeX to Linear transformer to show how equations will appear
in Word's equation editor.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from typing import Optional, Dict, Any
import logging

try:
    from latex_to_linear_transformer import LaTeXToLinearTransformer
    TRANSFORMER_AVAILABLE = True
except ImportError:
    TRANSFORMER_AVAILABLE = False
    LaTeXToLinearTransformer = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EquationPreviewWidget:
    """
    Integrated equation preview widget for the LaTeX Extractor
    
    This widget provides real-time preview of mathematical equations
    showing both the LaTeX input and the Word Linear format output.
    """
    
    def __init__(self, parent_frame: tk.Widget, settings: Optional[Dict] = None):
        """
        Initialize the equation preview widget
        
        Args:
            parent_frame: Parent tkinter widget
            settings: Configuration settings
        """
        self.parent = parent_frame
        self.settings = settings or {}
        
        # Initialize transformer if available
        if TRANSFORMER_AVAILABLE:
            self.transformer = LaTeXToLinearTransformer(self.settings)
        else:
            self.transformer = None
            
        # Widget state
        self.current_latex = ""
        self.current_linear = ""
        self.preview_mode = "linear"  # "linear", "visual", "both"
        
        # Create the widget UI
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the preview widget user interface"""
        # Main container
        self.main_frame = ttk.LabelFrame(self.parent, text="📐 Equation Preview", padding=5)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control panel
        self.create_control_panel()
        
        # Preview area
        self.create_preview_area()
        
        # Status bar
        self.create_status_bar()
        
    def create_control_panel(self):
        """Create the control panel with preview options"""
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Preview mode selection
        ttk.Label(control_frame, text="Preview Mode:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.mode_var = tk.StringVar(value=self.preview_mode)
        mode_combo = ttk.Combobox(control_frame, textvariable=self.mode_var, 
                                 values=["linear", "visual", "both"], 
                                 state="readonly", width=10)
        mode_combo.pack(side=tk.LEFT, padx=(0, 10))
        mode_combo.bind('<<ComboboxSelected>>', self.on_mode_changed)
        
        # Refresh button
        ttk.Button(control_frame, text="🔄 Refresh", 
                  command=self.refresh_preview).pack(side=tk.LEFT, padx=2)
        
        # Copy buttons
        ttk.Button(control_frame, text="📋 Copy Linear", 
                  command=self.copy_linear_format).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(control_frame, text="📄 Export to Word", 
                  command=self.export_to_word).pack(side=tk.LEFT, padx=2)
        
        # Settings button
        ttk.Button(control_frame, text="⚙️ Settings", 
                  command=self.open_settings).pack(side=tk.RIGHT, padx=2)
        
    def create_preview_area(self):
        """Create the main preview area"""
        # Create notebook for different preview modes
        self.preview_notebook = ttk.Notebook(self.main_frame)
        self.preview_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Linear format tab
        self.create_linear_preview_tab()
        
        # Visual preview tab
        self.create_visual_preview_tab()
        
        # Comparison tab
        self.create_comparison_tab()
        
    def create_linear_preview_tab(self):
        """Create the Linear format preview tab"""
        linear_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(linear_frame, text="📝 Linear Format")
        
        # Linear format display
        ttk.Label(linear_frame, text="Word Linear Format:").pack(anchor=tk.W, pady=(0, 5))
        
        self.linear_text = tk.Text(linear_frame, height=8, wrap=tk.WORD, 
                                  font=("Consolas", 11), bg="#f8f8f8")
        self.linear_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Add scrollbar
        linear_scroll = ttk.Scrollbar(linear_frame, orient=tk.VERTICAL, 
                                     command=self.linear_text.yview)
        self.linear_text.config(yscrollcommand=linear_scroll.set)
        linear_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Validation info
        self.validation_frame = ttk.LabelFrame(linear_frame, text="Validation", padding=5)
        self.validation_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.validation_label = ttk.Label(self.validation_frame, text="No equation to validate")
        self.validation_label.pack(anchor=tk.W)
        
    def create_visual_preview_tab(self):
        """Create the visual equation preview tab"""
        visual_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(visual_frame, text="👁️ Visual Preview")
        
        # Create matplotlib figure for equation rendering
        self.fig = Figure(figsize=(8, 4), dpi=100, facecolor='white')
        self.ax = self.fig.add_subplot(111)
        self.ax.set_xlim(0, 10)
        self.ax.set_ylim(0, 5)
        self.ax.axis('off')
        
        # Embed matplotlib in tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, visual_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Visual preview controls
        visual_controls = ttk.Frame(visual_frame)
        visual_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(visual_controls, text="Font Size:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.font_size_var = tk.IntVar(value=14)
        font_scale = ttk.Scale(visual_controls, from_=10, to=24, 
                              variable=self.font_size_var, orient=tk.HORIZONTAL)
        font_scale.pack(side=tk.LEFT, padx=(0, 10))
        font_scale.bind('<Motion>', self.on_font_size_changed)
        
        ttk.Button(visual_controls, text="🎨 Customize", 
                  command=self.customize_visual).pack(side=tk.RIGHT, padx=2)
        
    def create_comparison_tab(self):
        """Create the comparison tab showing LaTeX vs Linear"""
        comp_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(comp_frame, text="🔄 Comparison")
        
        # Create two-column layout
        left_frame = ttk.LabelFrame(comp_frame, text="LaTeX Input", padding=5)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))
        
        right_frame = ttk.LabelFrame(comp_frame, text="Linear Output", padding=5)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(2, 0))
        
        # LaTeX input display
        self.latex_display = tk.Text(left_frame, height=10, wrap=tk.WORD, 
                                    font=("Consolas", 10), bg="#fff8dc")
        self.latex_display.pack(fill=tk.BOTH, expand=True)
        
        # Linear output display
        self.linear_display = tk.Text(right_frame, height=10, wrap=tk.WORD, 
                                     font=("Consolas", 10), bg="#f0fff0")
        self.linear_display.pack(fill=tk.BOTH, expand=True)
        
        # Comparison controls
        comp_controls = ttk.Frame(comp_frame)
        comp_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(comp_controls, text="📊 Analyze Differences", 
                  command=self.analyze_differences).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(comp_controls, text="📈 Show Statistics", 
                  command=self.show_statistics).pack(side=tk.LEFT, padx=2)
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.status_label = ttk.Label(self.status_frame, text="Ready", 
                                     foreground="darkgreen")
        self.status_label.pack(side=tk.LEFT)
        
        # Transformer status
        if TRANSFORMER_AVAILABLE:
            transformer_status = "✅ Transformer Ready"
            color = "darkgreen"
        else:
            transformer_status = "❌ Transformer Not Available"
            color = "red"
            
        self.transformer_status = ttk.Label(self.status_frame, text=transformer_status, 
                                           foreground=color)
        self.transformer_status.pack(side=tk.RIGHT)
        
    def update_preview(self, latex_text: str):
        """
        Update the preview with new LaTeX text
        
        Args:
            latex_text: LaTeX equation to preview
        """
        if not latex_text or not latex_text.strip():
            self.clear_preview()
            return
            
        self.current_latex = latex_text
        self.status_label.config(text="Processing...", foreground="orange")
        
        try:
            if self.transformer:
                # Convert to Linear format
                self.current_linear = self.transformer.transform_latex_to_linear(latex_text)
                
                # Update all preview modes
                self.update_linear_preview()
                self.update_visual_preview()
                self.update_comparison_preview()
                
                self.status_label.config(text="Preview updated", foreground="darkgreen")
            else:
                self.status_label.config(text="Transformer not available", foreground="red")
                self.show_fallback_preview(latex_text)
                
        except Exception as e:
            logger.error(f"Preview update failed: {e}")
            self.status_label.config(text=f"Error: {str(e)}", foreground="red")
            self.show_error_preview(str(e))
            
    def update_linear_preview(self):
        """Update the Linear format preview"""
        # Clear and update linear text
        self.linear_text.delete(1.0, tk.END)
        self.linear_text.insert(1.0, self.current_linear)
        
        # Update validation info
        if self.transformer:
            validation = self.transformer.validate_linear_format(self.current_linear)
            
            if validation['is_valid']:
                status_text = f"✅ Valid Linear format (Complexity: {validation['complexity_score']})"
                color = "darkgreen"
            else:
                issues = ', '.join(validation['issues'])
                status_text = f"❌ Issues: {issues}"
                color = "red"
                
            self.validation_label.config(text=status_text, foreground=color)
        
    def update_visual_preview(self):
        """Update the visual equation preview"""
        try:
            # Clear the plot
            self.ax.clear()
            self.ax.set_xlim(0, 10)
            self.ax.set_ylim(0, 5)
            self.ax.axis('off')
            
            # Create a visual representation of the equation
            # This is a simplified representation - in a full implementation,
            # you might use matplotlib's mathtext or integrate with MathJax
            
            # Display the Linear format as styled text
            font_size = self.font_size_var.get()
            
            # Position the equation in the center
            self.ax.text(5, 2.5, self.current_linear, 
                        fontsize=font_size, ha='center', va='center',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
            
            # Add a title
            self.ax.text(5, 4, "Word Linear Format Preview", 
                        fontsize=font_size-2, ha='center', va='center',
                        style='italic', color='darkblue')
            
            # Refresh the canvas
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"Visual preview update failed: {e}")
            
    def update_comparison_preview(self):
        """Update the comparison preview"""
        # Update LaTeX display
        self.latex_display.delete(1.0, tk.END)
        self.latex_display.insert(1.0, self.current_latex)
        
        # Update Linear display
        self.linear_display.delete(1.0, tk.END)
        self.linear_display.insert(1.0, self.current_linear)
        
    def clear_preview(self):
        """Clear all preview areas"""
        self.current_latex = ""
        self.current_linear = ""
        
        # Clear text widgets
        self.linear_text.delete(1.0, tk.END)
        self.latex_display.delete(1.0, tk.END)
        self.linear_display.delete(1.0, tk.END)
        
        # Clear visual preview
        self.ax.clear()
        self.ax.set_xlim(0, 10)
        self.ax.set_ylim(0, 5)
        self.ax.axis('off')
        self.ax.text(5, 2.5, "No equation to preview", 
                    ha='center', va='center', fontsize=12, color='gray')
        self.canvas.draw()
        
        # Update status
        self.status_label.config(text="Ready", foreground="darkgreen")
        self.validation_label.config(text="No equation to validate", foreground="black")
        
    def show_fallback_preview(self, latex_text: str):
        """Show fallback preview when transformer is not available"""
        self.linear_text.delete(1.0, tk.END)
        self.linear_text.insert(1.0, f"Transformer not available.\nOriginal LaTeX: {latex_text}")
        
        self.validation_label.config(text="❌ Cannot validate - transformer not available", 
                                   foreground="red")
        
    def show_error_preview(self, error_message: str):
        """Show error message in preview"""
        self.linear_text.delete(1.0, tk.END)
        self.linear_text.insert(1.0, f"Error during conversion:\n{error_message}")
        
        self.validation_label.config(text="❌ Conversion failed", foreground="red")
        
    def on_mode_changed(self, event=None):
        """Handle preview mode change"""
        self.preview_mode = self.mode_var.get()
        # You could implement different display modes here
        
    def on_font_size_changed(self, event=None):
        """Handle font size change in visual preview"""
        if self.current_linear:
            self.update_visual_preview()
            
    def refresh_preview(self):
        """Refresh the current preview"""
        if self.current_latex:
            self.update_preview(self.current_latex)
            
    def copy_linear_format(self):
        """Copy the Linear format to clipboard"""
        if self.current_linear:
            self.parent.clipboard_clear()
            self.parent.clipboard_append(self.current_linear)
            messagebox.showinfo("Copied", "Linear format copied to clipboard!")
        else:
            messagebox.showwarning("No Content", "No Linear format to copy")
            
    def export_to_word(self):
        """Export the current equation to a Word document"""
        if not self.current_linear:
            messagebox.showwarning("No Content", "No equation to export")
            return
            
        try:
            if self.transformer:
                filename = "equation_preview.docx"
                self.transformer.export_to_docx([self.current_linear], filename)
                messagebox.showinfo("Exported", f"Equation exported to {filename}")
            else:
                messagebox.showerror("Error", "Transformer not available for export")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export: {str(e)}")
            
    def open_settings(self):
        """Open settings dialog for the preview widget"""
        # This would open a settings dialog
        messagebox.showinfo("Settings", "Settings dialog would open here")
        
    def customize_visual(self):
        """Open visual customization dialog"""
        messagebox.showinfo("Customize", "Visual customization dialog would open here")
        
    def analyze_differences(self):
        """Analyze differences between LaTeX and Linear format"""
        if not self.current_latex or not self.current_linear:
            messagebox.showwarning("No Content", "Need both LaTeX and Linear format to compare")
            return
            
        # Simple analysis
        latex_len = len(self.current_latex)
        linear_len = len(self.current_linear)
        
        analysis = f"""Comparison Analysis:
        
LaTeX Length: {latex_len} characters
Linear Length: {linear_len} characters
Reduction: {latex_len - linear_len} characters ({((latex_len - linear_len) / latex_len * 100):.1f}%)

LaTeX Commands Removed: {self.current_latex.count('\\')}
Braces Removed: {self.current_latex.count('{') + self.current_latex.count('}') - self.current_linear.count('{') - self.current_linear.count('}')}
"""
        
        messagebox.showinfo("Analysis Results", analysis)
        
    def show_statistics(self):
        """Show conversion statistics"""
        if self.transformer and self.current_linear:
            validation = self.transformer.validate_linear_format(self.current_linear)
            
            stats = f"""Conversion Statistics:
            
Complexity Score: {validation['complexity_score']}
Character Count: {validation['character_count']}
Validation Status: {'✅ Valid' if validation['is_valid'] else '❌ Invalid'}

Issues Found: {len(validation['issues'])}
"""
            if validation['issues']:
                stats += f"Issues: {', '.join(validation['issues'])}"
                
            messagebox.showinfo("Statistics", stats)
        else:
            messagebox.showwarning("No Data", "No statistics available")


# Convenience function for easy integration
def create_equation_preview_widget(parent_frame: tk.Widget, 
                                  settings: Optional[Dict] = None) -> EquationPreviewWidget:
    """
    Convenience function to create an equation preview widget
    
    Args:
        parent_frame: Parent tkinter widget
        settings: Optional settings dictionary
        
    Returns:
        EquationPreviewWidget instance
    """
    return EquationPreviewWidget(parent_frame, settings)
