#!/usr/bin/env python3
"""
LaTeX to Linear Format Transformer

This module provides comprehensive conversion from LaTeX expressions (from OCR)
to Microsoft Word's Linear equation format, with intermediate OMML processing.

Pipeline:
1. OCR LaTeX Normalization - Clean and normalize raw LaTeX from OCR
2. LaTeX to MathML (Optional) - Intermediate representation for complex expressions
3. LaTeX to OMML - Convert to Office Math Markup Language
4. OMML to Linear - Extract Word's Linear format from OMML
5. Word Integration - Insert and render equations in Word documents

Author: LaTeX Extractor by Yark
"""

import re
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple, Union
from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LaTeXToLinearTransformer:
    """
    Comprehensive LaTeX to Word Linear format transformer
    
    This class handles the complete pipeline from raw OCR LaTeX to Word-compatible
    Linear format equations that can be edited in Microsoft Word's equation editor.
    """
    
    def __init__(self, settings: Optional[Dict] = None):
        """
        Initialize the transformer with optional settings
        
        Args:
            settings: Configuration dictionary for conversion behavior
        """
        self.settings = settings or {
            'minimal_spacing': True,      # Use minimal spacing like (1+2x) not (1 + 2 * x)
            'avoid_extra_brackets': True, # Minimize unnecessary brackets
            'use_unicode_symbols': True,  # Use × for multiplication, ⇒ for implies
            'debug_mode': False,          # Enable detailed logging
            'preserve_alignment': True,   # Handle alignment structures
            'word_compatibility': True    # Ensure full Word compatibility
        }
        
        # Initialize conversion mappings
        self._init_symbol_mappings()
        self._init_command_mappings()
        
    def _init_symbol_mappings(self):
        """Initialize LaTeX symbol to Unicode/Linear mappings"""
        self.symbol_map = {
            # Greek letters
            '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ', '\\delta': 'δ',
            '\\epsilon': 'ε', '\\theta': 'θ', '\\lambda': 'λ', '\\mu': 'μ',
            '\\pi': 'π', '\\sigma': 'σ', '\\phi': 'φ', '\\omega': 'ω',
            '\\Gamma': 'Γ', '\\Delta': 'Δ', '\\Theta': 'Θ', '\\Lambda': 'Λ',
            
            # Mathematical operators
            '\\times': '×', '\\div': '÷', '\\cdot': '·',
            '\\pm': '±', '\\mp': '∓',
            '\\leq': '≤', '\\geq': '≥', '\\neq': '≠',
            '\\approx': '≈', '\\equiv': '≡', '\\cong': '≅',
            '\\rightarrow': '→', '\\Rightarrow': '⇒',
            '\\leftarrow': '←', '\\Leftarrow': '⇐',
            
            # Special symbols
            '\\infty': '∞', '\\partial': '∂', '\\nabla': '∇',
            '\\sum': '∑', '\\prod': '∏', '\\int': '∫',
            
            # Dots and spacing
            '\\cdots': '⋯', '\\ldots': '…', '\\vdots': '⋮', '\\ddots': '⋱'
        }
        
    def _init_command_mappings(self):
        """Initialize LaTeX command to Linear format mappings"""
        self.command_map = {
            'frac': self._convert_fraction_to_linear,
            'sqrt': self._convert_sqrt_to_linear,
            'sum': self._convert_sum_to_linear,
            'int': self._convert_integral_to_linear,
            'lim': self._convert_limit_to_linear,
            'mathrm': self._convert_text_to_linear,
            'text': self._convert_text_to_linear,
            'textrm': self._convert_text_to_linear
        }

    def transform_latex_to_linear(self, latex_text: str) -> str:
        """
        Main transformation method: LaTeX → Linear format
        
        Args:
            latex_text: Raw LaTeX text from OCR
            
        Returns:
            Word Linear format string
        """
        try:
            # Step 1: Normalize OCR LaTeX
            normalized_latex = self.normalize_ocr_latex(latex_text)
            logger.info(f"Normalized LaTeX: {normalized_latex}")
            
            # Step 2: Convert to OMML (intermediate)
            omml_xml = self.latex_to_omml(normalized_latex)
            logger.info(f"Generated OMML: {len(omml_xml)} characters")
            
            # Step 3: Extract Linear format from OMML
            linear_format = self.omml_to_linear(omml_xml)
            logger.info(f"Linear format: {linear_format}")
            
            # Step 4: Post-process for Word compatibility
            final_linear = self.optimize_for_word(linear_format)
            
            return final_linear
            
        except Exception as e:
            logger.error(f"Transformation failed: {e}")
            # Fallback to basic conversion
            return self.basic_latex_to_linear(latex_text)

    def normalize_ocr_latex(self, latex_text: str) -> str:
        """
        Step 1: Clean and normalize raw LaTeX from OCR tools
        
        Handles common OCR errors, spacing issues, and syntax irregularities
        """
        if not latex_text or not latex_text.strip():
            return ""
            
        # Remove common LaTeX delimiters
        latex_text = latex_text.strip()
        latex_text = re.sub(r'^\$+|\$+$', '', latex_text)  # Remove $ delimiters
        latex_text = re.sub(r'^\\[\[\(]|\\[\]\)]$', '', latex_text)  # Remove \[ \] \( \)
        
        # Fix common OCR spacing issues
        latex_text = re.sub(r'\s+', ' ', latex_text)  # Normalize whitespace
        latex_text = re.sub(r'\s*\{\s*', '{', latex_text)  # Fix { spacing
        latex_text = re.sub(r'\s*\}\s*', '}', latex_text)  # Fix } spacing
        
        # Fix common OCR command recognition errors
        latex_text = re.sub(r'\\mathrm\s*\{', r'\\mathrm{', latex_text)
        latex_text = re.sub(r'\\frac\s*\{', r'\\frac{', latex_text)
        latex_text = re.sub(r'\\sqrt\s*\{', r'\\sqrt{', latex_text)
        
        # Handle problematic LaTeX constructs for Word
        latex_text = self._fix_word_incompatible_commands(latex_text)
        
        return latex_text.strip()

    def _fix_word_incompatible_commands(self, latex_text: str) -> str:
        """Fix LaTeX commands that don't work well in Word"""
        
        # Remove sizing commands that Word handles automatically
        sizing_commands = [r'\\left\b', r'\\right\b', r'\\big[glr]?\b', 
                          r'\\Big[glr]?\b', r'\\bigg[glr]?\b', r'\\Bigg[glr]?\b']
        for cmd in sizing_commands:
            latex_text = re.sub(cmd, '', latex_text)
            
        # Convert spacing commands to appropriate spacing
        latex_text = re.sub(r'\\,', ' ', latex_text)  # Thin space
        latex_text = re.sub(r'\\:', ' ', latex_text)  # Medium space
        latex_text = re.sub(r'\\;', ' ', latex_text)  # Thick space
        latex_text = re.sub(r'\\quad\b', '  ', latex_text)  # Quad space
        
        # Handle array environments - convert to simpler aligned format
        latex_text = self._process_array_environments(latex_text)
        
        return latex_text

    def _process_array_environments(self, latex_text: str) -> str:
        """Convert LaTeX array environments to Word-compatible format"""

        # Handle \begin{array} environments
        array_pattern = r'\\begin\{array\}\{[^}]*\}(.*?)\\end\{array\}'

        def replace_array(match):
            content = match.group(1)
            # Split by \\ for rows and & for columns
            rows = content.split('\\\\')
            processed_rows = []

            for row in rows:
                if row.strip():
                    # Replace & with appropriate spacing/operators
                    clean_row = re.sub(r'\s*&\s*=\s*', ' = ', row)
                    clean_row = re.sub(r'\s*&\s*', ' ', clean_row)
                    processed_rows.append(clean_row.strip())

            # Join with a unique separator that we can later convert to line breaks
            return ' LINEBREAK '.join(processed_rows)

        latex_text = re.sub(array_pattern, replace_array, latex_text, flags=re.DOTALL)

        # Also handle \begin{aligned} environments for better multi-line support
        aligned_pattern = r'\\begin\{aligned\}(.*?)\\end\{aligned\}'

        def replace_aligned(match):
            content = match.group(1)
            # Split by \\ for rows
            rows = content.split('\\\\')
            processed_rows = []

            for row in rows:
                if row.strip():
                    # Handle alignment markers properly
                    clean_row = re.sub(r'\s*&\s*=\s*', ' = ', row)
                    clean_row = re.sub(r'\s*&\s*', ' ', clean_row)
                    processed_rows.append(clean_row.strip())

            # Join with a unique separator that we can later convert to line breaks
            return ' LINEBREAK '.join(processed_rows)

        latex_text = re.sub(aligned_pattern, replace_aligned, latex_text, flags=re.DOTALL)
        return latex_text

    def latex_to_omml(self, latex_text: str) -> str:
        """
        Step 2-3: Convert LaTeX to OMML (Office Math Markup Language)
        
        This creates the intermediate XML representation that Word uses internally
        """
        try:
            # Parse LaTeX into tokens
            tokens = self._tokenize_latex(latex_text)
            
            # Convert tokens to OMML elements
            omml_content = self._tokens_to_omml(tokens)
            
            # Wrap in proper OMML structure
            omml_xml = f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                {omml_content}
            </m:oMath>'''
            
            return omml_xml
            
        except Exception as e:
            logger.error(f"OMML conversion failed: {e}")
            # Fallback to simple text representation
            return f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                <m:r><m:t>{latex_text}</m:t></m:r>
            </m:oMath>'''

    def _tokenize_latex(self, latex_text: str) -> List[Dict]:
        """Tokenize LaTeX text into manageable components"""
        tokens = []
        i = 0
        
        while i < len(latex_text):
            if latex_text[i] == '\\':
                # LaTeX command
                token, end_pos = self._parse_latex_command(latex_text, i)
                tokens.append(token)
                i = end_pos
            elif latex_text[i] in ['^', '_']:
                # Superscript/subscript
                token, end_pos = self._parse_script(latex_text, i)
                tokens.append(token)
                i = end_pos
            elif latex_text[i] in ['{', '}']:
                # Skip braces - they're handled by command parsing
                i += 1
            else:
                # Regular text
                j = i
                while j < len(latex_text) and latex_text[j] not in ['\\', '^', '_', '{', '}']:
                    j += 1
                if j > i:
                    tokens.append({'type': 'text', 'value': latex_text[i:j]})
                    i = j
                else:
                    i += 1
                    
        return tokens

    def _parse_latex_command(self, latex_text: str, start_pos: int) -> Tuple[Dict, int]:
        """Parse LaTeX commands with their arguments"""
        i = start_pos + 1  # Skip the backslash
        
        # Get command name
        cmd_start = i
        while i < len(latex_text) and latex_text[i].isalpha():
            i += 1
        command = latex_text[cmd_start:i]
        
        # Parse arguments if present
        args = []
        while i < len(latex_text) and latex_text[i].isspace():
            i += 1
            
        while i < len(latex_text) and latex_text[i] == '{':
            arg, end_pos = self._parse_braced_content(latex_text, i)
            args.append(arg)
            i = end_pos
            
        return {'type': 'command', 'name': command, 'args': args}, i

    def _parse_script(self, latex_text: str, start_pos: int) -> Tuple[Dict, int]:
        """Parse superscript (^) or subscript (_) expressions"""
        script_type = 'superscript' if latex_text[start_pos] == '^' else 'subscript'
        i = start_pos + 1
        
        # Skip whitespace
        while i < len(latex_text) and latex_text[i].isspace():
            i += 1
            
        # Parse content
        if i < len(latex_text) and latex_text[i] == '{':
            content, end_pos = self._parse_braced_content(latex_text, i)
        else:
            # Single character
            content = latex_text[i] if i < len(latex_text) else ''
            end_pos = i + 1
            
        return {'type': script_type, 'content': content}, end_pos

    def _parse_braced_content(self, latex_text: str, start_pos: int) -> Tuple[str, int]:
        """Parse content within braces, handling nested braces"""
        if start_pos >= len(latex_text) or latex_text[start_pos] != '{':
            return '', start_pos
            
        i = start_pos + 1
        brace_count = 1
        content_start = i
        
        while i < len(latex_text) and brace_count > 0:
            if latex_text[i] == '{':
                brace_count += 1
            elif latex_text[i] == '}':
                brace_count -= 1
            i += 1
            
        content = latex_text[content_start:i-1] if brace_count == 0 else latex_text[content_start:]
        return content, i

    def _tokens_to_omml(self, tokens: List[Dict]) -> str:
        """Convert parsed tokens to OMML elements"""
        omml_parts = []
        i = 0
        
        while i < len(tokens):
            token = tokens[i]
            
            if token['type'] == 'text':
                omml_parts.append(f'<m:r><m:t>{self._escape_xml(token["value"])}</m:t></m:r>')
                
            elif token['type'] == 'command':
                omml_parts.append(self._convert_command_to_omml(token))
                
            elif token['type'] in ['superscript', 'subscript']:
                # Combine with previous element if available
                if omml_parts:
                    base = omml_parts.pop()
                    script_omml = self._create_script_omml(token, base)
                    omml_parts.append(script_omml)
                else:
                    # Standalone script
                    omml_parts.append(self._create_script_omml(token, '<m:r><m:t></m:t></m:r>'))
                    
            i += 1
            
        return ''.join(omml_parts)

    def _convert_command_to_omml(self, token: Dict) -> str:
        """Convert LaTeX commands to OMML"""
        command = token['name']
        args = token.get('args', [])
        
        # Check if we have a specific converter for this command
        if command in self.command_map:
            return self.command_map[command](args)
            
        # Check symbol mappings
        full_command = f'\\{command}'
        if full_command in self.symbol_map:
            symbol = self.symbol_map[full_command]
            return f'<m:r><m:t>{symbol}</m:t></m:r>'
            
        # Fallback to text representation
        return f'<m:r><m:t>{command}</m:t></m:r>'

    def _convert_fraction_to_linear(self, args: List[str]) -> str:
        """Convert \frac{num}{den} to OMML fraction"""
        if len(args) < 2:
            return '<m:r><m:t>frac</m:t></m:r>'
            
        num_omml = self._latex_to_omml_recursive(args[0])
        den_omml = self._latex_to_omml_recursive(args[1])
        
        return f'''<m:f>
            <m:fPr></m:fPr>
            <m:num>{num_omml}</m:num>
            <m:den>{den_omml}</m:den>
        </m:f>'''

    def _convert_sqrt_to_linear(self, args: List[str]) -> str:
        r"""Convert \sqrt{expr} to OMML radical"""
        if not args:
            return '<m:r><m:t>√</m:t></m:r>'

        expr_omml = self._latex_to_omml_recursive(args[0])

        return f'''<m:rad>
            <m:radPr></m:radPr>
            <m:deg></m:deg>
            <m:e>{expr_omml}</m:e>
        </m:rad>'''

    def _convert_sum_to_linear(self, args: List[str]) -> str:
        r"""Convert \sum with limits to OMML"""
        base_omml = '<m:r><m:t>∑</m:t></m:r>'

        if len(args) >= 2:
            lower_omml = self._latex_to_omml_recursive(args[0])
            upper_omml = self._latex_to_omml_recursive(args[1])

            return f'''<m:sSubSup>
                <m:sSubSupPr></m:sSubSupPr>
                <m:e>{base_omml}</m:e>
                <m:sub>{lower_omml}</m:sub>
                <m:sup>{upper_omml}</m:sup>
            </m:sSubSup>'''

        return base_omml

    def _convert_integral_to_linear(self, args: List[str]) -> str:
        r"""Convert \int with limits to OMML"""
        base_omml = '<m:r><m:t>∫</m:t></m:r>'

        if len(args) >= 2:
            lower_omml = self._latex_to_omml_recursive(args[0])
            upper_omml = self._latex_to_omml_recursive(args[1])

            return f'''<m:sSubSup>
                <m:sSubSupPr></m:sSubSupPr>
                <m:e>{base_omml}</m:e>
                <m:sub>{lower_omml}</m:sub>
                <m:sup>{upper_omml}</m:sup>
            </m:sSubSup>'''

        return base_omml

    def _convert_limit_to_linear(self, args: List[str]) -> str:
        r"""Convert \lim to OMML function"""
        if not args:
            return '<m:r><m:t>lim</m:t></m:r>'

        limit_expr = self._latex_to_omml_recursive(args[0])

        return f'''<m:func>
            <m:funcPr></m:funcPr>
            <m:fName><m:r><m:t>lim</m:t></m:r></m:fName>
            <m:e>{limit_expr}</m:e>
        </m:func>'''

    def _convert_text_to_linear(self, args: List[str]) -> str:
        r"""Convert \mathrm{text} to plain text OMML"""
        if not args:
            return '<m:r><m:t></m:t></m:r>'

        return f'<m:r><m:t>{self._escape_xml(args[0])}</m:t></m:r>'

    def _create_script_omml(self, token: Dict, base_omml: str) -> str:
        """Create superscript or subscript OMML"""
        content_omml = self._latex_to_omml_recursive(token['content'])
        
        if token['type'] == 'superscript':
            return f'''<m:sSup>
                <m:sSupPr></m:sSupPr>
                <m:e>{base_omml}</m:e>
                <m:sup>{content_omml}</m:sup>
            </m:sSup>'''
        else:  # subscript
            return f'''<m:sSub>
                <m:sSubPr></m:sSubPr>
                <m:e>{base_omml}</m:e>
                <m:sub>{content_omml}</m:sub>
            </m:sSub>'''

    def _latex_to_omml_recursive(self, latex_text: str) -> str:
        """Recursively convert LaTeX to OMML for nested expressions"""
        if not latex_text or not latex_text.strip():
            return '<m:r><m:t></m:t></m:r>'
            
        # For simple text, return directly
        if not any(char in latex_text for char in ['\\', '^', '_', '{', '}']):
            return f'<m:r><m:t>{self._escape_xml(latex_text)}</m:t></m:r>'
            
        # For complex expressions, tokenize and convert
        tokens = self._tokenize_latex(latex_text)
        return self._tokens_to_omml(tokens)

    def _escape_xml(self, text: str) -> str:
        """Escape XML special characters"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&apos;'))

    def omml_to_linear(self, omml_xml: str) -> str:
        """
        Step 4: Convert OMML to Word Linear format

        This extracts the Linear equation syntax that Word's equation editor uses
        """
        try:
            # Parse the OMML XML
            root = ET.fromstring(omml_xml)

            # Extract linear format from OMML structure
            linear_parts = self._extract_linear_from_omml(root)

            # Join and clean up the result
            linear_format = ''.join(linear_parts)
            return self._clean_linear_format(linear_format)

        except Exception as e:
            logger.error(f"OMML to Linear conversion failed: {e}")
            # Fallback to basic text extraction
            return self._extract_text_from_omml(omml_xml)

    def _extract_linear_from_omml(self, element: ET.Element) -> List[str]:
        """Extract Linear format from OMML XML elements"""
        linear_parts = []

        # Get the tag name without namespace
        tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag

        if tag == 'oMath':
            # Root math element - process children
            for child in element:
                linear_parts.extend(self._extract_linear_from_omml(child))

        elif tag == 'r':
            # Text run - extract text content
            for child in element:
                if child.tag.endswith('t'):
                    text = child.text or ''
                    # Fix spacing issues with tildes and common text patterns
                    text = text.replace('for~', 'for ')
                    text = text.replace('~', ' ')
                    linear_parts.append(text)

        elif tag == 'f':
            # Fraction - convert to Linear format
            num_parts = []
            den_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'num':
                    num_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'den':
                    den_parts.extend(self._extract_linear_from_omml(child))

            num_text = ''.join(num_parts).strip()
            den_text = ''.join(den_parts).strip()

            # Format as Linear fraction with improved readability for multi-line equations
            # For derivatives and complex expressions, use clearer formatting
            if 'd' in num_text and ('dt' in den_text or 'dx' in den_text or 'dy' in den_text):
                # Derivative notation: keep clear separation
                linear_parts.append(f"({num_text})/({den_text})")
            elif self._needs_parentheses_for_linear(num_text) or self._needs_parentheses_for_linear(den_text):
                # Complex expressions need parentheses
                if not num_text.startswith('('):
                    num_text = f"({num_text})" if self._needs_parentheses_for_linear(num_text) else num_text
                if not den_text.startswith('('):
                    den_text = f"({den_text})" if self._needs_parentheses_for_linear(den_text) else den_text
                linear_parts.append(f"{num_text}/{den_text}")
            else:
                # Simple fractions
                linear_parts.append(f"{num_text}/{den_text}")

        elif tag == 'rad':
            # Square root - convert to Linear format
            expr_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'e':
                    expr_parts.extend(self._extract_linear_from_omml(child))

            expr_text = ''.join(expr_parts).strip()
            linear_parts.append(f"√({expr_text})")

        elif tag == 'sSup':
            # Superscript - convert to Linear format
            base_parts = []
            sup_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'e':
                    base_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'sup':
                    sup_parts.extend(self._extract_linear_from_omml(child))

            base_text = ''.join(base_parts).strip()
            sup_text = ''.join(sup_parts).strip()

            # Format with minimal parentheses
            if self._needs_parentheses_for_script(sup_text):
                sup_text = f"({sup_text})"

            linear_parts.append(f"{base_text}^{sup_text}")

        elif tag == 'sSub':
            # Subscript - convert to Linear format
            base_parts = []
            sub_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'e':
                    base_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'sub':
                    sub_parts.extend(self._extract_linear_from_omml(child))

            base_text = ''.join(base_parts).strip()
            sub_text = ''.join(sub_parts).strip()

            # Format with minimal parentheses
            if self._needs_parentheses_for_script(sub_text):
                sub_text = f"({sub_text})"

            linear_parts.append(f"{base_text}_{sub_text}")

        elif tag == 'sSubSup':
            # Combined subscript and superscript
            base_parts = []
            sub_parts = []
            sup_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'e':
                    base_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'sub':
                    sub_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'sup':
                    sup_parts.extend(self._extract_linear_from_omml(child))

            base_text = ''.join(base_parts).strip()
            sub_text = ''.join(sub_parts).strip()
            sup_text = ''.join(sup_parts).strip()

            # Format with minimal parentheses
            if self._needs_parentheses_for_script(sub_text):
                sub_text = f"({sub_text})"
            if self._needs_parentheses_for_script(sup_text):
                sup_text = f"({sup_text})"

            linear_parts.append(f"{base_text}_{sub_text}^{sup_text}")

        elif tag == 'func':
            # Function - convert to Linear format
            name_parts = []
            arg_parts = []

            for child in element:
                child_tag = child.tag.split('}')[-1]
                if child_tag == 'fName':
                    name_parts.extend(self._extract_linear_from_omml(child))
                elif child_tag == 'e':
                    arg_parts.extend(self._extract_linear_from_omml(child))

            name_text = ''.join(name_parts).strip()
            arg_text = ''.join(arg_parts).strip()

            if arg_text:
                linear_parts.append(f"{name_text}({arg_text})")
            else:
                linear_parts.append(name_text)

        else:
            # For other elements, process children
            for child in element:
                linear_parts.extend(self._extract_linear_from_omml(child))

        return linear_parts

    def _needs_parentheses_for_linear(self, text: str) -> bool:
        """Determine if text needs parentheses in Linear format"""
        if not text or not text.strip():
            return False

        text = text.strip()

        # Simple cases that don't need parentheses
        if re.match(r'^[a-zA-Z0-9]+$', text):  # Single variable/number
            return False
        if re.match(r'^-[a-zA-Z0-9]+$', text):  # Single negative
            return False
        if text.startswith('(') and text.endswith(')'):  # Already has parentheses
            return False

        # Check for operations that need parentheses
        if any(op in text for op in ['+', '-', '*', '/', ' ']):
            return True

        return False

    def _needs_parentheses_for_script(self, text: str) -> bool:
        """Determine if text needs parentheses in superscript/subscript"""
        if not text or not text.strip():
            return False

        text = text.strip()

        # Single characters don't need parentheses
        if len(text) == 1:
            return False

        # Simple expressions don't need parentheses
        if re.match(r'^[a-zA-Z0-9]+$', text):
            return False

        # Already has parentheses
        if text.startswith('(') and text.endswith(')'):
            return False

        # Multiple characters or operations need parentheses
        return len(text) > 1 and any(op in text for op in ['+', '-', '*', '/', ' ', '='])

    def _clean_linear_format(self, linear_text: str) -> str:
        """Clean and optimize Linear format for Word compatibility"""
        if not linear_text:
            return ""

        # Remove extra spaces
        linear_text = re.sub(r'\s+', ' ', linear_text)
        linear_text = linear_text.strip()

        # Apply minimal spacing preferences
        if self.settings.get('minimal_spacing', True):
            linear_text = self._apply_minimal_spacing(linear_text)

        # Remove unnecessary parentheses
        if self.settings.get('avoid_extra_brackets', True):
            linear_text = self._remove_unnecessary_parentheses(linear_text)

        return linear_text

    def _apply_minimal_spacing(self, text: str) -> str:
        """Apply minimal spacing rules for cleaner appearance"""
        # Remove spaces around operators in simple cases
        text = re.sub(r'(\w)\s*\+\s*(\w)', r'\1+\2', text)  # a + b → a+b
        text = re.sub(r'(\w)\s*-\s*(\w)', r'\1-\2', text)   # a - b → a-b
        text = re.sub(r'(\w)\s*\*\s*(\w)', r'\1*\2', text)  # a * b → a*b
        text = re.sub(r'(\d)\s*([a-zA-Z])', r'\1\2', text)  # 2 x → 2x

        return text

    def _remove_unnecessary_parentheses(self, text: str) -> str:
        """Remove parentheses that aren't needed for clarity"""
        # Remove parentheses around single variables/numbers
        text = re.sub(r'\(([a-zA-Z0-9])\)', r'\1', text)

        # Remove double parentheses
        text = re.sub(r'\(\(([^)]+)\)\)', r'(\1)', text)

        return text

    def _extract_text_from_omml(self, omml_xml: str) -> str:
        """Fallback method to extract plain text from OMML"""
        try:
            # Remove XML tags and extract text content
            text = re.sub(r'<[^>]+>', '', omml_xml)
            text = text.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
            text = text.replace('&quot;', '"').replace('&apos;', "'")
            return text.strip()
        except:
            return omml_xml

    def optimize_for_word(self, linear_text: str) -> str:
        """
        Step 5: Final optimization for Word compatibility

        Ensures the Linear format works perfectly in Word's equation editor
        """
        if not linear_text:
            return ""

        # Apply Word-specific formatting rules
        optimized = linear_text

        # Ensure proper multiplication notation
        optimized = self._fix_multiplication_for_word(optimized)

        # Handle factorial notation
        optimized = self._fix_factorial_notation(optimized)

        # Ensure proper parentheses matching
        optimized = self._ensure_balanced_parentheses(optimized)

        # Apply final cleanup
        optimized = self._final_cleanup_for_word(optimized)

        return optimized

    def _fix_multiplication_for_word(self, text: str) -> str:
        """Fix multiplication notation for Word compatibility"""
        # Ensure explicit multiplication with factorials
        text = re.sub(r'(\d+)!(\w)', r'\1!*\2', text)  # 2!x → 2!*x
        text = re.sub(r'(\w)(\d+)!', r'\1*\2!', text)  # x2! → x*2!

        # Fix implicit multiplication that Word might not parse correctly
        text = re.sub(r'(\))(\w)', r'\1*\2', text)     # (x)y → (x)*y
        text = re.sub(r'(\w)(\()', r'\1*\2', text)     # x(y) → x*(y)

        # But preserve simple cases like 2x
        text = re.sub(r'(\d)([a-zA-Z])', r'\1\2', text)  # Keep 2x as 2x

        return text

    def _fix_factorial_notation(self, text: str) -> str:
        """Fix factorial notation for Word compatibility"""
        # Ensure factorials are clearly separated
        text = re.sub(r'(\w)!(\w)', r'\1! \2', text)   # x!y → x! y
        text = re.sub(r'(\d)!(\d)', r'\1! \2', text)   # 5!3 → 5! 3

        return text

    def _ensure_balanced_parentheses(self, text: str) -> str:
        """Ensure parentheses are properly balanced for Word"""
        # Count parentheses
        open_count = text.count('(')
        close_count = text.count(')')

        # Add missing closing parentheses
        if open_count > close_count:
            text += ')' * (open_count - close_count)
        # Remove extra closing parentheses
        elif close_count > open_count:
            extra_closes = close_count - open_count
            for _ in range(extra_closes):
                # Remove the last extra closing parenthesis
                last_close = text.rfind(')')
                if last_close != -1:
                    text = text[:last_close] + text[last_close+1:]

        return text

    def _final_cleanup_for_word(self, text: str) -> str:
        """Final cleanup for Word compatibility"""
        # Remove multiple spaces
        text = re.sub(r'\s+', ' ', text)

        # Remove spaces around certain operators
        text = re.sub(r'\s*([+\-*/^_])\s*', r'\1', text)

        # Ensure proper spacing around equals signs
        text = re.sub(r'\s*=\s*', ' = ', text)

        # Fix double operators that can occur during conversion
        text = re.sub(r'([+\-*/^_])\*([+\-*/^_])', r'\1\2', text)  # Remove * between operators
        text = re.sub(r'_\*\(', r'_(', text)  # Fix _*( to _(
        text = re.sub(r'\)\*_', r')_', text)  # Fix )*_ to )_
        text = re.sub(r'\*_\*', r'_', text)   # Fix *_* to _
        text = re.sub(r'\^_\*', r'^_', text)  # Fix ^_* to ^_

        # Clean up any remaining issues
        text = text.strip()

        return text

    def basic_latex_to_linear(self, latex_text: str) -> str:
        """
        Fallback method: Basic LaTeX to Linear conversion

        Used when the full pipeline fails, provides basic conversion
        """
        if not latex_text:
            return ""

        # Start with the input
        result = latex_text

        # Remove LaTeX delimiters
        result = re.sub(r'^\$+|\$+$', '', result)
        result = re.sub(r'^\\[\[\(]|\\[\]\)]$', '', result)

        # Convert basic fractions with improved formatting
        def format_fraction(match):
            num = match.group(1)
            den = match.group(2)

            # Special handling for derivatives
            if 'd' in num and ('dt' in den or 'dx' in den or 'dy' in den):
                return f"({num})/({den})"
            # Simple single character fractions
            elif len(num) == 1 and len(den) == 1:
                return f"{num}/{den}"
            # Complex fractions
            else:
                return f"({num})/({den})"

        result = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', format_fraction, result)

        # Convert basic square roots
        result = re.sub(r'\\sqrt\{([^}]+)\}', r'√(\1)', result)

        # Convert basic superscripts and subscripts
        result = re.sub(r'\^\{([^}]+)\}', r'^(\1)', result)
        result = re.sub(r'_\{([^}]+)\}', r'_(\1)', result)

        # Convert symbols
        for latex_symbol, unicode_symbol in self.symbol_map.items():
            result = result.replace(latex_symbol, unicode_symbol)

        # Remove remaining LaTeX commands
        result = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', result)
        result = re.sub(r'\\[a-zA-Z]+', '', result)

        # Clean up braces
        result = re.sub(r'\{([^}]*)\}', r'\1', result)

        # Final cleanup
        result = re.sub(r'\s+', ' ', result)
        result = result.strip()

        return result

    def format_multiline_linear(self, linear_text: str) -> str:
        """Format multi-line linear equations for better readability"""

        # Handle different line separator patterns
        separators_to_check = ['LINEBREAK', '\\n', ' n', 'n']

        for separator in separators_to_check:
            if separator in linear_text:
                lines = linear_text.split(separator)
                formatted_lines = []

                for line in lines:
                    line = line.strip()
                    if line:
                        # Add proper spacing around operators
                        line = re.sub(r'\s*=\s*', ' = ', line)
                        line = re.sub(r'\s*\+\s*', ' + ', line)
                        line = re.sub(r'\s*-\s*', ' - ', line)
                        line = re.sub(r'\s*\*\s*', ' * ', line)

                        # Clean up multiple spaces
                        line = re.sub(r'\s+', ' ', line)
                        formatted_lines.append(line)

                if len(formatted_lines) > 1:  # Only format if we actually split into multiple lines
                    return '\n'.join(formatted_lines)
                break

        # If no multi-line separators found, check if it's a long single line that should be split
        # Look for patterns that indicate equation boundaries
        if len(linear_text) > 100:  # Long equations might need splitting
            # Try to split on equation patterns like "= ... something ="
            equation_pattern = r'(\s*=\s*[^=]+?)(?=\s*[a-zA-Z_]\w*\s*[+\-]|\s*\([^)]+\)\s*[+\-])'
            parts = re.split(equation_pattern, linear_text)

            if len(parts) > 3:  # If we found multiple equation parts
                formatted_lines = []
                current_line = ""

                for part in parts:
                    if part.strip():
                        if '=' in part and current_line:
                            formatted_lines.append(current_line.strip())
                            current_line = part.strip()
                        else:
                            current_line += part

                if current_line.strip():
                    formatted_lines.append(current_line.strip())

                if len(formatted_lines) > 1:
                    return '\n'.join(formatted_lines)

        return linear_text

    def render_in_word(self, linear_text: str, doc: Optional[Document] = None) -> Document:
        """
        Step 6: Render the Linear format equation in a Word document

        Args:
            linear_text: The Linear format equation
            doc: Optional existing document to add to

        Returns:
            Word document with the equation
        """
        if doc is None:
            doc = Document()

        # Convert Linear format back to OMML for Word insertion
        omml_xml = self.linear_to_omml(linear_text)

        # Create a paragraph for the equation
        para = doc.add_paragraph()
        para.alignment = 1  # Center alignment

        try:
            # Parse and insert the OMML
            math_element = parse_xml(omml_xml)
            para._element.append(math_element)
        except Exception as e:
            logger.error(f"Failed to insert OMML: {e}")
            # Fallback to plain text
            para.add_run(linear_text)

        return doc

    def linear_to_omml(self, linear_text: str) -> str:
        """Convert Linear format back to OMML for Word insertion"""
        # This is a simplified reverse conversion
        # In practice, Word handles Linear format directly

        # For now, we'll create a simple OMML wrapper
        escaped_text = self._escape_xml(linear_text)

        return f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
            <m:r><m:t>{escaped_text}</m:t></m:r>
        </m:oMath>'''

    def export_to_docx(self, equations: List[str], filename: str) -> str:
        """
        Export multiple Linear format equations to a Word document

        Args:
            equations: List of Linear format equations
            filename: Output filename

        Returns:
            Path to the created document
        """
        doc = Document()

        # Set document properties
        doc.core_properties.title = "Mathematical Equations - Linear Format"
        doc.core_properties.author = "LaTeX Extractor by Yark"

        # Add title
        title = doc.add_heading('Mathematical Equations', 0)
        title.alignment = 1  # Center alignment

        # Add each equation
        for i, equation in enumerate(equations, 1):
            # Add equation header
            header = doc.add_paragraph()
            header_run = header.add_run(f"Equation {i}")
            header_run.bold = True

            # Add the equation
            self.render_in_word(equation, doc)

            # Add spacing
            doc.add_paragraph()

        # Save the document
        doc.save(filename)
        return filename

    def get_linear_preview(self, latex_text: str) -> str:
        """
        Get a preview of how the LaTeX will look in Linear format

        Args:
            latex_text: Input LaTeX text

        Returns:
            Linear format preview string
        """
        try:
            return self.transform_latex_to_linear(latex_text)
        except Exception as e:
            logger.error(f"Preview generation failed: {e}")
            return f"Preview error: {str(e)}"

    def validate_linear_format(self, linear_text: str) -> Dict[str, Union[bool, List[str]]]:
        """
        Validate Linear format for Word compatibility

        Args:
            linear_text: Linear format text to validate

        Returns:
            Dictionary with validation results
        """
        issues = []

        # Check parentheses balance
        if linear_text.count('(') != linear_text.count(')'):
            issues.append("Unbalanced parentheses")

        # Check for problematic characters
        problematic_chars = ['\\', '{', '}', '$']
        for char in problematic_chars:
            if char in linear_text:
                issues.append(f"Contains problematic character: {char}")

        # Check for double operators
        if re.search(r'[+\-*/^_]{2,}', linear_text):
            issues.append("Contains double operators")

        # Check for empty parentheses
        if '()' in linear_text:
            issues.append("Contains empty parentheses")

        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'character_count': len(linear_text),
            'complexity_score': self._calculate_complexity(linear_text)
        }

    def _calculate_complexity(self, text: str) -> int:
        """Calculate complexity score for the equation"""
        score = 0
        score += text.count('/') * 2      # Fractions
        score += text.count('^') * 1      # Superscripts
        score += text.count('_') * 1      # Subscripts
        score += text.count('√') * 2      # Square roots
        score += text.count('∑') * 3      # Summations
        score += text.count('∫') * 3      # Integrals
        score += len(re.findall(r'[(){}]', text))  # Grouping symbols

        return score


# Convenience functions for easy integration
def latex_to_linear(latex_text: str, settings: Optional[Dict] = None) -> str:
    """
    Convenience function to convert LaTeX to Linear format

    Args:
        latex_text: Input LaTeX text
        settings: Optional conversion settings

    Returns:
        Linear format string
    """
    transformer = LaTeXToLinearTransformer(settings)
    return transformer.transform_latex_to_linear(latex_text)


def create_word_document_with_equations(equations: List[str], filename: str,
                                       settings: Optional[Dict] = None) -> str:
    """
    Convenience function to create Word document with Linear format equations

    Args:
        equations: List of Linear format equations
        filename: Output filename
        settings: Optional conversion settings

    Returns:
        Path to created document
    """
    transformer = LaTeXToLinearTransformer(settings)
    return transformer.export_to_docx(equations, filename)


def validate_equation(linear_text: str) -> Dict[str, Union[bool, List[str]]]:
    """
    Convenience function to validate Linear format equation

    Args:
        linear_text: Linear format text to validate

    Returns:
        Validation results dictionary
    """
    transformer = LaTeXToLinearTransformer()
    return transformer.validate_linear_format(linear_text)
