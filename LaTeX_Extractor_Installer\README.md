# LaTeX Extractor by Yark - Installation Guide

## Overview
LaTeX Extractor by <PERSON><PERSON> is a powerful mathematical equation processor that converts images to LaTeX code and exports to Microsoft Word format.

## System Requirements
- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 2GB free disk space
- Internet connection for initial AI model download

## Installation Instructions

### Automatic Installation (Recommended)
1. Run `Scripts\install.bat` as Administrator
2. Follow the on-screen prompts
3. Wait for installation to complete
4. Launch from Desktop shortcut or Start Menu

### Manual Installation
If automatic installation fails:
1. Install Python 3.13.3 from `Dependencies\python-3.13.3-amd64.exe`
2. Install Tesseract OCR from `Dependencies\tesseract-ocr-w64-setup-5.5.0.20241111.exe`
3. Install Ollama from `Dependencies\OllamaSetup.exe`
4. Run `Dependencies\install_packages.bat` to install Python packages
5. Copy `Application\*` to your desired installation directory
6. Run `LaTeX_Extractor.bat` to start the application

## Features
- **Mathematics OCR**: Convert mathematical images to LaTeX code
- **LaTeX Editing**: Built-in LaTeX editor with live preview
- **Word Compatibility**: Export equations to Microsoft Word format
- **Batch Processing**: Process multiple images at once
- **AI Enhancement**: Optional AI-powered LaTeX improvement

## Troubleshooting

### "No OCR methods are available" Error
- Ensure all dependencies are installed correctly
- Run the installer as Administrator
- Check that pix2tex package is installed: `python -c "import pix2tex"`

### "LaTeX-OCR Not Available" Error
- Verify PyTorch installation: `python -c "import torch"`
- Check internet connection for model download
- Restart the application after installation

### Performance Issues
- Ensure sufficient RAM (8GB recommended)
- Close other applications during processing
- Use SSD storage for better performance

## Support
- **Phone**: +92 309 2656986
- **Website**: https://yark.com
- **Email**: <EMAIL>

## Version Information
- Version: 2.0.0
- Build Date: 2025
- Compatible with: Windows 10/11 64-bit

---
© 2025 Yark. All rights reserved.
