@echo off
title LaTeX Extractor by Yark - Uninstaller

echo ================================================================
echo LaTeX Extractor by Yark - Uninstaller
echo ================================================================
echo.

set /p CONFIRM="Are you sure you want to uninstall LaTeX Extractor by Yark? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Uninstallation cancelled.
    pause
    exit /b 0
)

echo.
echo 🗑️ Uninstalling LaTeX Extractor by Yark...

REM Remove application directory
set INSTALL_DIR=%PROGRAMFILES%\LaTeX Extractor by Yark
if exist "%INSTALL_DIR%" (
    rmdir /s /q "%INSTALL_DIR%"
    echo ✅ Application files removed
)

REM Remove desktop shortcut
del "%USERPROFILE%\Desktop\LaTeX Extractor by Yark.lnk" 2>nul
echo ✅ Desktop shortcut removed

REM Remove start menu shortcut
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\LaTeX Extractor by Yark" 2>nul
echo ✅ Start menu shortcut removed

echo.
echo ✅ LaTeX Extractor by Yark has been uninstalled successfully!
echo.
pause
