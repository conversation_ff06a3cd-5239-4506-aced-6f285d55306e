# LaTeX Extractor by Yark - Dependencies for Standalone Installer
# Version: 2.0.0
# Target: Windows 10/11 64-bit

# Image processing
Pillow>=9.0.0
opencv-python>=4.5.0
numpy>=1.21.0

# PDF handling
PyMuPDF>=1.20.0
pdf2image>=1.16.0

# OCR
pytesseract>=0.3.10
pix2tex>=0.1.2

# Deep Learning (for LaTeX-OCR and Llama 3)
torch>=1.9.0
torchvision>=0.10.0
transformers>=4.37.0

# AI for LaTeX Enhancement (lightweight and efficient)
accelerate>=0.20.0
protobuf>=3.20.0

# System Performance Monitoring (optional)
psutil>=5.9.0

# Word document creation
python-docx>=0.8.11
lxml>=4.6.0

# Additional utilities
typing-extensions>=4.0.0

# Installer-specific dependencies
requests>=2.25.0