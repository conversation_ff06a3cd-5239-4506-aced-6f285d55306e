#!/usr/bin/env python3
"""
LaTeX Extractor by Yark - Startup Script
Handles application startup with dependency validation and configuration
"""

import os
import sys
import time
from pathlib import Path

def show_startup_banner():
    """Show startup banner"""
    print("=" * 60)
    print("LaTeX Extractor by Yark v2.0.0")
    print("Mathematical Equation Processor")
    print("=" * 60)
    print("🚀 Starting application...")

def check_python_version():
    """Check Python version compatibility"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print(f"✅ Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

def check_dependencies():
    """Check if required dependencies are available with enhanced OCR verification"""
    print("🔍 Checking dependencies...")

    missing_deps = []

    # Check critical imports
    try:
        import tkinter
        print("✅ Tkinter available")
    except ImportError:
        missing_deps.append("tkinter")

    try:
        import PIL
        print("✅ Pillow available")
    except ImportError:
        missing_deps.append("Pillow")

    try:
        import cv2
        print("✅ OpenCV available")
    except ImportError:
        missing_deps.append("opencv-python")

    try:
        import numpy
        print("✅ NumPy available")
    except ImportError:
        missing_deps.append("numpy")

    try:
        import fitz
        print("✅ PyMuPDF available")
    except ImportError:
        missing_deps.append("PyMuPDF")

    # Enhanced OCR dependency checking
    try:
        import pytesseract
        print("✅ pytesseract available")
    except ImportError:
        missing_deps.append("pytesseract")
        print("❌ pytesseract not available")

    # Critical LaTeX-OCR check
    try:
        import pix2tex
        print("✅ pix2tex (LaTeX-OCR) available")

        # Try to initialize LaTeX-OCR to verify it works
        try:
            from pix2tex.cli import LatexOCR
            model = LatexOCR()
            print("✅ LaTeX-OCR model initialized successfully")
        except Exception as e:
            print(f"⚠️  LaTeX-OCR model initialization warning: {e}")

    except ImportError:
        missing_deps.append("pix2tex")
        print("❌ pix2tex (LaTeX-OCR) not available - This will cause OCR errors")

    # AI/ML dependencies for LaTeX-OCR
    try:
        import torch
        print("✅ PyTorch available")
    except ImportError:
        missing_deps.append("torch")
        print("❌ PyTorch not available - Required for LaTeX-OCR")

    try:
        import transformers
        print("✅ Transformers available")
    except ImportError:
        missing_deps.append("transformers")
        print("❌ Transformers not available")
    
    try:
        from pdf2image import convert_from_path
        print("✅ pdf2image available")
    except ImportError:
        missing_deps.append("pdf2image")
    
    try:
        from docx import Document
        print("✅ python-docx available")
    except ImportError:
        missing_deps.append("python-docx")
    
    if missing_deps:
        print("❌ Missing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n💡 Please install missing dependencies:")
        print("pip install " + " ".join(missing_deps))
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("✅ All critical dependencies available")

def configure_paths():
    """Configure dependency paths"""
    print("🔧 Configuring dependency paths...")
    
    try:
        from config.path_configurator import configure_dependencies
        path_config = configure_dependencies()
        
        # Validate critical paths
        validation = path_config.validate_paths()
        
        critical_deps = ['poppler_path', 'tesseract_path']
        missing_critical = []
        
        for dep in critical_deps:
            if not validation.get(dep, False):
                missing_critical.append(dep.replace('_path', ''))
        
        if missing_critical:
            print("⚠️  Some dependencies are not available:")
            for dep in missing_critical:
                print(f"  - {dep}")
            print("\n💡 The application will start but some features may not work.")
            print("Please ensure all dependencies are properly installed.")
        else:
            print("✅ All dependency paths configured")
        
        return path_config
        
    except Exception as e:
        print(f"⚠️  Path configuration warning: {e}")
        print("Application will start with default configuration")
        return None

def setup_environment():
    """Setup application environment"""
    print("🌍 Setting up environment...")
    
    # Create user directories
    user_dirs = [
        Path.home() / "Documents" / "LaTeX Extractor",
        Path.home() / "AppData" / "Local" / "LaTeX Extractor" / "temp",
        Path.home() / "AppData" / "Local" / "LaTeX Extractor" / "cache",
        Path.home() / "AppData" / "Local" / "LaTeX Extractor" / "logs"
    ]
    
    for directory in user_dirs:
        try:
            directory.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"⚠️  Could not create directory {directory}: {e}")
    
    print("✅ Environment setup completed")

def check_first_run():
    """Check if this is the first run and show welcome message"""
    first_run_file = Path.home() / "AppData" / "Local" / "LaTeX Extractor" / ".first_run"
    
    if not first_run_file.exists():
        print("\n🎉 Welcome to LaTeX Extractor by Yark!")
        print("This appears to be your first run.")
        print("\n📋 Quick Start Guide:")
        print("1. Click 'Import Files' to load PDF files or images")
        print("2. Select equations from the preview")
        print("3. Review and edit the LaTeX code")
        print("4. Export to Word format")
        print("\n💡 For detailed help, check the Help menu")
        
        try:
            first_run_file.parent.mkdir(parents=True, exist_ok=True)
            first_run_file.touch()
        except Exception:
            pass
        
        print("\nPress Enter to continue...")
        input()

def start_application():
    """Start the main application"""
    print("🎯 Starting LaTeX Extractor...")
    
    try:
        # Import and start the main application
        from main import LaTeXExtractorByYark
        
        print("✅ Application loaded successfully")
        print("🖥️  Opening main window...")
        
        app = LaTeXExtractorByYark()
        app.run()
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all dependencies are installed")
        print("2. Check that Python packages are up to date")
        print("3. Verify system requirements are met")
        print("4. Contact support if the problem persists")
        print("\n📞 Support: +92 309 2656986")
        print("🌐 Website: https://yark.com")
        
        input("Press Enter to exit...")
        sys.exit(1)

def main():
    """Main startup function"""
    try:
        # Show banner
        show_startup_banner()
        
        # Check Python version
        check_python_version()
        
        # Check dependencies
        check_dependencies()
        
        # Configure paths
        path_config = configure_paths()
        
        # Setup environment
        setup_environment()
        
        # Check first run
        check_first_run()
        
        # Start application
        start_application()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Application startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        print("\n🔧 Please check the installation and try again")
        print("📞 Support: +92 309 2656986")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
