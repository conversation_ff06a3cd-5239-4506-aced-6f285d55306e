"""
Multi-line Equation Parser for LaTeX Extractor

This module provides robust parsing and handling of multi-line LaTeX equations,
ensuring they are processed as complete expressions rather than line-by-line.
"""

import re
import logging
from typing import Dict, List, Tuple, Optional

logger = logging.getLogger(__name__)


class MultilineEquationParser:
    """
    Parser for handling multi-line LaTeX equations properly
    """
    
    def __init__(self):
        """Initialize the multi-line equation parser"""
        self.multiline_environments = [
            'align', 'align*', 'aligned', 'alignat', 'alignat*',
            'gather', 'gather*', 'gathered',
            'multline', 'multline*',
            'split', 'array', 'matrix', 'pmatrix', 'bmatrix',
            'vmatrix', 'Vmatrix', 'cases', 'eqnarray', 'eqnarray*'
        ]
        
    def is_multiline_equation(self, latex_text: str) -> bool:
        """
        Check if the LaTeX text contains multi-line equation structures
        
        Args:
            latex_text: LaTeX text to check
            
        Returns:
            True if the text contains multi-line structures
        """
        if not latex_text:
            return False
            
        # Check for line breaks
        if '\\\\' in latex_text:
            return True
            
        # Check for multi-line environments
        for env in self.multiline_environments:
            if f'\\begin{{{env}}}' in latex_text or f'\\end{{{env}}}' in latex_text:
                return True
                
        # Check for alignment markers
        if '&' in latex_text and ('=' in latex_text or '\\approx' in latex_text or '\\equiv' in latex_text):
            return True
            
        return False
        
    def extract_complete_equation(self, latex_text: str) -> str:
        """
        Extract and preserve the complete multi-line equation structure
        
        Args:
            latex_text: Raw LaTeX text that may contain incomplete equations
            
        Returns:
            Complete LaTeX equation with proper structure preserved
        """
        if not self.is_multiline_equation(latex_text):
            return latex_text.strip()
            
        # Clean up the input while preserving structure
        cleaned_text = self._clean_input_preserving_structure(latex_text)
        
        # Ensure proper environment wrapping
        wrapped_text = self._ensure_proper_environment(cleaned_text)
        
        # Validate and fix alignment
        validated_text = self._validate_and_fix_alignment(wrapped_text)
        
        return validated_text
        
    def _clean_input_preserving_structure(self, latex_text: str) -> str:
        """Clean input while preserving multi-line structure"""
        # Remove outer delimiters but preserve inner structure
        text = latex_text.strip()
        
        # Remove outer $ delimiters
        if text.startswith('$') and text.endswith('$'):
            text = text[1:-1]
        elif text.startswith('$$') and text.endswith('$$'):
            text = text[2:-2]
        elif text.startswith('\\[') and text.endswith('\\]'):
            text = text[2:-2]
        elif text.startswith('\\(') and text.endswith('\\)'):
            text = text[2:-2]
            
        # Clean up excessive whitespace but preserve line structure
        lines = text.split('\\\\')
        cleaned_lines = []
        
        for line in lines:
            # Clean each line individually
            cleaned_line = re.sub(r'\s+', ' ', line.strip())
            if cleaned_line:  # Only add non-empty lines
                cleaned_lines.append(cleaned_line)
                
        return '\\\\'.join(cleaned_lines)
        
    def _ensure_proper_environment(self, latex_text: str) -> str:
        """Ensure the equation has proper environment wrapping"""
        # Check if already in an environment
        has_environment = any(f'\\begin{{{env}}}' in latex_text for env in self.multiline_environments)
        
        if has_environment:
            return latex_text
            
        # If it has line breaks or alignment, wrap in aligned environment
        if '\\\\' in latex_text or '&' in latex_text:
            return f'\\begin{{aligned}}\n{latex_text}\n\\end{{aligned}}'
            
        return latex_text
        
    def _validate_and_fix_alignment(self, latex_text: str) -> str:
        """Validate and fix alignment markers in multi-line equations"""
        if '&' not in latex_text:
            return latex_text
            
        lines = latex_text.split('\\\\')
        fixed_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Ensure proper alignment marker placement
            if '&' in line:
                # Fix common alignment issues
                line = re.sub(r'\s*&\s*=\s*', ' &= ', line)  # Standardize &= spacing
                line = re.sub(r'\s*&\s*', ' & ', line)  # Standardize & spacing
                
            fixed_lines.append(line)
            
        return '\\\\'.join(fixed_lines)
        
    def split_into_logical_parts(self, latex_text: str) -> List[Dict]:
        """
        Split multi-line equation into logical parts for processing
        
        Args:
            latex_text: Complete multi-line LaTeX equation
            
        Returns:
            List of equation parts with metadata
        """
        if not self.is_multiline_equation(latex_text):
            return [{'type': 'single', 'content': latex_text, 'line_number': 1}]
            
        parts = []
        
        # Handle environment-wrapped equations
        env_match = re.search(r'\\begin\{([^}]+)\}(.*?)\\end\{\1\}', latex_text, re.DOTALL)
        if env_match:
            env_name = env_match.group(1)
            content = env_match.group(2).strip()
            
            # Split by line breaks
            lines = content.split('\\\\')
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    parts.append({
                        'type': 'multiline_part',
                        'content': line,
                        'line_number': i,
                        'environment': env_name,
                        'total_lines': len([l for l in lines if l.strip()])
                    })
        else:
            # Handle raw multi-line content
            lines = latex_text.split('\\\\')
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    parts.append({
                        'type': 'multiline_part',
                        'content': line,
                        'line_number': i,
                        'environment': None,
                        'total_lines': len([l for l in lines if l.strip()])
                    })
                    
        return parts
        
    def reconstruct_equation(self, parts: List[Dict]) -> str:
        """
        Reconstruct complete equation from logical parts
        
        Args:
            parts: List of equation parts from split_into_logical_parts
            
        Returns:
            Complete reconstructed LaTeX equation
        """
        if not parts:
            return ""
            
        if len(parts) == 1 and parts[0]['type'] == 'single':
            return parts[0]['content']
            
        # Reconstruct multi-line equation
        lines = [part['content'] for part in parts if part['content'].strip()]
        content = '\\\\'.join(lines)
        
        # Determine if we need environment wrapping
        if parts and parts[0].get('environment'):
            env = parts[0]['environment']
            return f'\\begin{{{env}}}\n{content}\n\\end{{{env}}}'
        elif len(parts) > 1:
            return f'\\begin{{aligned}}\n{content}\n\\end{{aligned}}'
        else:
            return content
            
    def is_equation_complete(self, latex_text: str) -> Tuple[bool, List[str]]:
        """
        Check if a multi-line equation is complete and properly formatted
        
        Args:
            latex_text: LaTeX equation to validate
            
        Returns:
            Tuple of (is_complete, list_of_issues)
        """
        issues = []
        
        if not latex_text or not latex_text.strip():
            return False, ["Empty equation"]
            
        # Check for unmatched environments
        for env in self.multiline_environments:
            begin_count = latex_text.count(f'\\begin{{{env}}}')
            end_count = latex_text.count(f'\\end{{{env}}}')
            if begin_count != end_count:
                issues.append(f"Unmatched {env} environment")
                
        # Check for unmatched braces
        open_braces = latex_text.count('{')
        close_braces = latex_text.count('}')
        if open_braces != close_braces:
            issues.append("Unmatched braces")
            
        # Check for proper line endings in multi-line equations
        if self.is_multiline_equation(latex_text):
            lines = latex_text.split('\\\\')
            if len(lines) > 1:
                # Check if lines have proper content
                empty_lines = [i for i, line in enumerate(lines) if not line.strip()]
                if empty_lines:
                    issues.append(f"Empty lines at positions: {empty_lines}")
                    
        # Check for alignment consistency
        if '&' in latex_text:
            lines = latex_text.split('\\\\')
            alignment_counts = [line.count('&') for line in lines if line.strip()]
            if alignment_counts and len(set(alignment_counts)) > 1:
                issues.append("Inconsistent alignment markers across lines")
                
        return len(issues) == 0, issues
        
    def get_equation_metadata(self, latex_text: str) -> Dict:
        """
        Get metadata about the equation structure
        
        Args:
            latex_text: LaTeX equation
            
        Returns:
            Dictionary with equation metadata
        """
        metadata = {
            'is_multiline': self.is_multiline_equation(latex_text),
            'line_count': 1,
            'has_alignment': '&' in latex_text,
            'environments': [],
            'complexity_score': 0
        }
        
        if metadata['is_multiline']:
            lines = latex_text.split('\\\\')
            metadata['line_count'] = len([line for line in lines if line.strip()])
            
        # Detect environments
        for env in self.multiline_environments:
            if f'\\begin{{{env}}}' in latex_text:
                metadata['environments'].append(env)
                
        # Calculate complexity score
        complexity = 0
        complexity += metadata['line_count'] * 2
        complexity += len(metadata['environments']) * 3
        complexity += latex_text.count('\\frac') * 2
        complexity += latex_text.count('\\sqrt') * 1
        complexity += latex_text.count('\\int') * 3
        complexity += latex_text.count('\\sum') * 3
        
        metadata['complexity_score'] = complexity
        
        return metadata
