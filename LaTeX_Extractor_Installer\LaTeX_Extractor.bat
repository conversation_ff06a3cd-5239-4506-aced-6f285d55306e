@echo off
title LaTeX Extractor by Yark v2.0.0

echo ================================================================
echo LaTeX Extractor by Yark v2.0.0
echo Mathematical Equation Processor
echo ================================================================
echo.

REM Set environment variables for better compatibility
set PYTHONIOENCODING=utf-8
set TESSDATA_PREFIX=%~dp0Dependencies\tesseract\tessdata

REM Add dependencies to PATH
set PATH=%~dp0Dependencies\poppler\bin;%~dp0Dependencies\tesseract;%PATH%

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python first or run the installer
    pause
    exit /b 1
)

echo [INFO] Starting LaTeX Extractor...

REM Launch the Python application
cd /d "%~dp0Application"
python main.py

if errorlevel 1 (
    echo.
    echo [ERROR] Application encountered an error.
    echo.
    echo Support: +92 309 2656986
    echo Website: https://yark.com
    pause
)
