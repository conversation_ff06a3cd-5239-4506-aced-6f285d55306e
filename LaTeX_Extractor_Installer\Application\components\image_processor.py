#!/usr/bin/env python3
"""
Image Processor Module for MathCapture Studio - Responsive Edition

Handles image processing operations for better OCR results.
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import logging
from typing import Tuple, Optional, Union

class ImageProcessor:
    """Advanced image processing for OCR optimization"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def enhance_image(self, image: np.ndarray, enhancement_level: str = "medium") -> np.ndarray:
        """Enhance image quality for better OCR results"""
        try:
            # Convert to PIL Image for enhancement
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)
                
            # Apply enhancements based on level
            if enhancement_level == "low":
                enhanced = self._apply_light_enhancement(pil_image)
            elif enhancement_level == "high":
                enhanced = self._apply_heavy_enhancement(pil_image)
            else:  # medium
                enhanced = self._apply_medium_enhancement(pil_image)
                
            # Convert back to numpy array
            if enhanced.mode == 'RGB':
                return cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
            else:
                return np.array(enhanced)
                
        except Exception as e:
            self.logger.error(f"Image enhancement failed: {e}")
            return image
            
    def _apply_light_enhancement(self, image: Image.Image) -> Image.Image:
        """Apply light enhancement"""
        # Slight contrast enhancement
        enhancer = ImageEnhance.Contrast(image)
        enhanced = enhancer.enhance(1.1)
        
        # Slight sharpening
        enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=0.5, percent=50, threshold=2))
        
        return enhanced
        
    def _apply_medium_enhancement(self, image: Image.Image) -> Image.Image:
        """Apply medium enhancement"""
        # Contrast enhancement
        enhancer = ImageEnhance.Contrast(image)
        enhanced = enhancer.enhance(1.2)
        
        # Brightness adjustment
        enhancer = ImageEnhance.Brightness(enhanced)
        enhanced = enhancer.enhance(1.05)
        
        # Sharpening
        enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=100, threshold=2))
        
        return enhanced
        
    def _apply_heavy_enhancement(self, image: Image.Image) -> Image.Image:
        """Apply heavy enhancement"""
        # Strong contrast enhancement
        enhancer = ImageEnhance.Contrast(image)
        enhanced = enhancer.enhance(1.3)
        
        # Brightness adjustment
        enhancer = ImageEnhance.Brightness(enhanced)
        enhanced = enhancer.enhance(1.1)
        
        # Color enhancement
        enhancer = ImageEnhance.Color(enhanced)
        enhanced = enhancer.enhance(1.1)
        
        # Strong sharpening
        enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1.5, percent=150, threshold=1))
        
        return enhanced
        
    def auto_crop(self, image: np.ndarray, padding: int = 10) -> np.ndarray:
        """Automatically crop image to content bounds"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
                
            # Apply threshold to find content
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return image
                
            # Get bounding box of all contours
            x_min, y_min = float('inf'), float('inf')
            x_max, y_max = 0, 0
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                x_min = min(x_min, x)
                y_min = min(y_min, y)
                x_max = max(x_max, x + w)
                y_max = max(y_max, y + h)
                
            # Add padding
            h, w = image.shape[:2]
            x_min = max(0, int(x_min) - padding)
            y_min = max(0, int(y_min) - padding)
            x_max = min(w, int(x_max) + padding)
            y_max = min(h, int(y_max) + padding)
            
            # Crop image
            return image[y_min:y_max, x_min:x_max]
            
        except Exception as e:
            self.logger.error(f"Auto crop failed: {e}")
            return image
            
    def correct_skew(self, image: np.ndarray) -> np.ndarray:
        """Correct skew in the image"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
                
            # Apply threshold
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Find lines using HoughLines
            edges = cv2.Canny(binary, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is None:
                return image
                
            # Calculate average angle
            angles = []
            for line in lines:
                rho, theta = line[0]
                angle = theta * 180 / np.pi
                # Convert to degrees and normalize
                if angle > 90:
                    angle = angle - 180
                angles.append(angle)
                
            if not angles:
                return image
                
            # Get median angle to avoid outliers
            median_angle = np.median(angles)
            
            # Only correct if skew is significant (> 0.5 degrees)
            if abs(median_angle) > 0.5:
                # Rotate image
                h, w = image.shape[:2]
                center = (w // 2, h // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                
                # Calculate new image size
                cos_angle = abs(rotation_matrix[0, 0])
                sin_angle = abs(rotation_matrix[0, 1])
                new_w = int((h * sin_angle) + (w * cos_angle))
                new_h = int((h * cos_angle) + (w * sin_angle))
                
                # Adjust rotation matrix for new center
                rotation_matrix[0, 2] += (new_w / 2) - center[0]
                rotation_matrix[1, 2] += (new_h / 2) - center[1]
                
                # Apply rotation
                rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h), 
                                       flags=cv2.INTER_CUBIC, 
                                       borderMode=cv2.BORDER_REPLICATE)
                return rotated
                
            return image
            
        except Exception as e:
            self.logger.error(f"Skew correction failed: {e}")
            return image
            
    def reduce_noise(self, image: np.ndarray) -> np.ndarray:
        """Reduce noise in the image"""
        try:
            # Apply different noise reduction based on image type
            if len(image.shape) == 3:
                # Color image - use Non-local Means Denoising
                denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            else:
                # Grayscale image
                denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
                
            return denoised
            
        except Exception as e:
            self.logger.error(f"Noise reduction failed: {e}")
            return image
            
    def enhance_contrast(self, image: np.ndarray, method: str = "clahe") -> np.ndarray:
        """Enhance contrast using various methods"""
        try:
            # Convert to grayscale if needed for CLAHE
            if len(image.shape) == 3:
                if method == "clahe":
                    # Apply CLAHE to each channel
                    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
                else:
                    # Histogram equalization
                    yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
                    yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
                    enhanced = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
            else:
                if method == "clahe":
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    enhanced = clahe.apply(image)
                else:
                    enhanced = cv2.equalizeHist(image)
                    
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Contrast enhancement failed: {e}")
            return image
            
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int] = None, 
                    scale_factor: float = None) -> np.ndarray:
        """Resize image while maintaining aspect ratio"""
        try:
            h, w = image.shape[:2]
            
            if target_size:
                target_w, target_h = target_size
                # Calculate scale to fit within target size
                scale = min(target_w / w, target_h / h)
            elif scale_factor:
                scale = scale_factor
            else:
                return image
                
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
            return resized
            
        except Exception as e:
            self.logger.error(f"Image resize failed: {e}")
            return image
            
    def apply_morphological_operations(self, image: np.ndarray, 
                                     operation: str = "close") -> np.ndarray:
        """Apply morphological operations to clean up the image"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
                
            # Apply threshold
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Define kernel
            kernel = np.ones((2, 2), np.uint8)
            
            if operation == "open":
                processed = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            elif operation == "close":
                processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            elif operation == "erode":
                processed = cv2.erode(binary, kernel, iterations=1)
            elif operation == "dilate":
                processed = cv2.dilate(binary, kernel, iterations=1)
            else:
                processed = binary
                
            # Convert back to original format if needed
            if len(image.shape) == 3:
                processed = cv2.cvtColor(processed, cv2.COLOR_GRAY2BGR)
                
            return processed
            
        except Exception as e:
            self.logger.error(f"Morphological operation failed: {e}")
            return image
            
    def process_for_ocr(self, image: np.ndarray, settings: dict = None) -> np.ndarray:
        """Apply a complete processing pipeline optimized for OCR"""
        settings = settings or {}
        processed = image.copy()
        
        try:
            # Apply enhancements based on settings
            if settings.get('image_enhancement', 'medium') != 'none':
                processed = self.enhance_image(processed, settings.get('image_enhancement', 'medium'))
                
            if settings.get('auto_crop', True):
                processed = self.auto_crop(processed)
                
            if settings.get('skew_correction', True):
                processed = self.correct_skew(processed)
                
            if settings.get('noise_reduction', True):
                processed = self.reduce_noise(processed)
                
            if settings.get('contrast_enhancement', 'clahe') != 'none':
                processed = self.enhance_contrast(processed, settings.get('contrast_enhancement', 'clahe'))
                
            return processed
            
        except Exception as e:
            self.logger.error(f"OCR processing pipeline failed: {e}")
            return image