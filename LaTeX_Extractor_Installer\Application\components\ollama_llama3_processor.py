#!/usr/bin/env python3
"""
Ollama Llama 3 Processor for LaTeX Enhancement
Uses local Ollama server for offline Llama 3 AI processing
"""

import requests
import json
import time
import subprocess
import os
from pathlib import Path
from typing import Dict, Optional, Any, List

class OllamaLlama3Processor:
    """Ollama-based Llama 3 processor for intelligent LaTeX enhancement"""
    
    def __init__(self, host="127.0.0.1", port=11434):
        """Initialize Ollama Llama 3 processor"""
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.model_name = "llama3.2:1b"  # Lightweight Llama 3.2 model
        self.available = False
        self.ollama_running = False
        
        print("🦙 Initializing Ollama Llama 3 processor...")
        
        try:
            self._check_ollama_installation()
            self._start_ollama_service()
            self._ensure_model_available()
            
            if self._test_connection():
                self.available = True
                print("✅ Ollama Llama 3 ready for LaTeX enhancement!")
            else:
                print("⚠️ Ollama connection failed - will use basic processing")
                
        except Exception as e:
            print(f"⚠️ Ollama Llama 3 initialization failed: {e}")
            print("🔄 Will use basic LaTeX processing")
            self.available = False
    
    def _check_ollama_installation(self):
        """Check if Ollama is installed"""
        ollama_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe".format(os.getenv('USERNAME')),
            r"C:\Program Files\Ollama\ollama.exe",
            "ollama.exe"  # In PATH
        ]
        
        for path in ollama_paths:
            if Path(path).exists() or (path == "ollama.exe" and self._command_exists("ollama")):
                print(f"✅ Ollama found at: {path}")
                return True
        
        raise Exception("Ollama not found. Please install Ollama first.")
    
    def _command_exists(self, command):
        """Check if command exists in PATH"""
        try:
            subprocess.run([command, "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _start_ollama_service(self):
        """Start Ollama service if not running"""
        if self._is_ollama_running():
            print("✅ Ollama service already running")
            self.ollama_running = True
            return
        
        print("🚀 Starting Ollama service...")
        try:
            # Start Ollama in background
            subprocess.Popen(
                ["ollama", "serve"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # Wait for service to start
            for i in range(30):  # Wait up to 30 seconds
                if self._is_ollama_running():
                    print("✅ Ollama service started")
                    self.ollama_running = True
                    return
                time.sleep(1)
            
            raise Exception("Ollama service failed to start")
            
        except Exception as e:
            raise Exception(f"Failed to start Ollama service: {e}")
    
    def _is_ollama_running(self):
        """Check if Ollama service is running"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def _ensure_model_available(self):
        """Ensure Llama 3 model is available"""
        print(f"🔍 Checking for {self.model_name} model...")
        
        try:
            # Check if model is already available
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                for model in models:
                    if model.get("name", "").startswith(self.model_name.split(":")[0]):
                        print(f"✅ Model {self.model_name} already available")
                        return
            
            # Pull the model if not available
            print(f"📥 Downloading {self.model_name} model (this may take a while)...")
            self._pull_model(self.model_name)
            
        except Exception as e:
            print(f"⚠️ Model check failed: {e}")
            # Try with a smaller model as fallback
            self.model_name = "llama3.2:1b"
            print(f"🔄 Trying fallback model: {self.model_name}")
            try:
                self._pull_model(self.model_name)
            except:
                raise Exception("Failed to download any Llama 3 model")
    
    def _pull_model(self, model_name):
        """Pull a model from Ollama"""
        try:
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                stream=True,
                timeout=300  # 5 minutes timeout
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        data = json.loads(line)
                        if "status" in data:
                            print(f"📥 {data['status']}")
                        if data.get("status") == "success":
                            print(f"✅ Model {model_name} downloaded successfully")
                            return
            else:
                raise Exception(f"Failed to pull model: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"Model download failed: {e}")
    
    def _test_connection(self):
        """Test connection to Ollama"""
        try:
            response = self._generate_completion("Test", max_tokens=5)
            return response is not None
        except:
            return False
    
    def _generate_completion(self, prompt, max_tokens=150, temperature=0.1):
        """Generate completion using Ollama"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature,
                    "top_p": 0.9
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                print(f"⚠️ Ollama API error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"⚠️ Ollama generation error: {e}")
            return None
    
    def is_available(self):
        """Check if Ollama Llama 3 is available"""
        return self.available and self.ollama_running
    
    def enhance_latex(self, latex_text: str) -> Dict[str, Any]:
        """
        Enhance LaTeX text using Llama 3
        
        Args:
            latex_text: Raw LaTeX text to enhance
            
        Returns:
            dict: Enhanced LaTeX with confidence and improvements
        """
        if not self.is_available():
            return {
                'enhanced_latex': latex_text,
                'confidence': 0.0,
                'improvements': [],
                'method': 'ollama_unavailable'
            }
        
        try:
            prompt = f"""You are a LaTeX expert. Please review and enhance this LaTeX expression for better formatting and correctness. Focus on:
1. Proper mathematical notation
2. Correct LaTeX syntax
3. Improved readability
4. Standard mathematical conventions

Original LaTeX: {latex_text}

Enhanced LaTeX (provide only the corrected LaTeX code):"""
            
            enhanced = self._generate_completion(prompt, max_tokens=200, temperature=0.1)
            
            if enhanced:
                # Extract just the LaTeX part if the model added explanations
                enhanced = self._extract_latex_from_response(enhanced)
                
                # Calculate confidence based on changes made
                confidence = self._calculate_enhancement_confidence(latex_text, enhanced)
                
                # Identify improvements
                improvements = self._identify_improvements(latex_text, enhanced)
                
                return {
                    'enhanced_latex': enhanced,
                    'confidence': confidence,
                    'improvements': improvements,
                    'method': 'ollama_llama3'
                }
            else:
                return {
                    'enhanced_latex': latex_text,
                    'confidence': 0.0,
                    'improvements': [],
                    'method': 'ollama_error'
                }
                
        except Exception as e:
            print(f"⚠️ LaTeX enhancement error: {e}")
            return {
                'enhanced_latex': latex_text,
                'confidence': 0.0,
                'improvements': [],
                'method': 'ollama_exception'
            }
    
    def _extract_latex_from_response(self, response):
        """Extract LaTeX code from model response"""
        # Remove common prefixes/suffixes
        response = response.strip()
        
        # Look for LaTeX code patterns
        if "```" in response:
            # Extract from code blocks
            parts = response.split("```")
            for part in parts:
                if "$" in part or "\\" in part:
                    return part.strip()
        
        # Look for LaTeX delimiters
        if "$" in response:
            # Extract content between $ delimiters
            parts = response.split("$")
            if len(parts) >= 3:
                return parts[1].strip()
        
        return response.strip()
    
    def _calculate_enhancement_confidence(self, original, enhanced):
        """Calculate confidence score for enhancement"""
        if original == enhanced:
            return 50.0  # No changes made
        
        # Basic confidence calculation
        confidence = 75.0
        
        # Increase confidence for common improvements
        improvements = [
            (r'\frac', 10),  # Proper fractions
            (r'\sqrt', 5),   # Square roots
            (r'\sum', 5),    # Summations
            (r'\int', 5),    # Integrals
            (r'\left', 10),  # Proper delimiters
            (r'\right', 10)
        ]
        
        for pattern, bonus in improvements:
            if pattern in enhanced and pattern not in original:
                confidence += bonus
        
        return min(95.0, confidence)
    
    def _identify_improvements(self, original, enhanced):
        """Identify specific improvements made"""
        improvements = []
        
        if original != enhanced:
            if len(enhanced) > len(original):
                improvements.append("Added proper LaTeX formatting")
            if r'\frac' in enhanced and r'\frac' not in original:
                improvements.append("Converted to proper fraction notation")
            if r'\left' in enhanced and r'\left' not in original:
                improvements.append("Added proper delimiter sizing")
            if enhanced.count('\\') > original.count('\\'):
                improvements.append("Enhanced mathematical notation")
        
        return improvements
    
    def cleanup(self):
        """Clean up resources"""
        # Note: We don't stop Ollama service as it might be used by other applications
        print("🧹 Ollama Llama 3 processor cleanup completed")
