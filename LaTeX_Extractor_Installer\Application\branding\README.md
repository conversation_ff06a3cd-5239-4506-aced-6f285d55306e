# LaTeX Extractor by Yark - Branding Assets

This folder contains placeholder files for Yark branding assets. Replace these placeholders with your actual logo and icons.

## Files to Replace

### 📱 Application Logo
- **logo.png** - Main application logo (recommended: 100x100px or larger)
  - Used in: About dialog
  - Format: PNG (preferred), JPG, JPEG, BMP, GIF
  - Will be automatically resized to 100x100px

### 🖼️ Window Icon
- **icon.ico** - Windows application icon (recommended: 32x32px, 48x48px, 64x64px)
  - Used in: Application title bar, taskbar
  - Format: ICO (Windows icon format)
  - Multiple sizes in one file preferred

- **icon.png** - Alternative icon format (recommended: 64x64px)
  - Used in: Application title bar (fallback)
  - Format: PNG

### 🎨 Additional Branding (Optional)
- **logo_large.png** - Large version for splash screen or documentation (recommended: 256x256px)
- **logo_small.png** - Small version for toolbar or status (recommended: 24x24px)
- **banner.png** - Horizontal banner for headers (recommended: 400x100px)

## How to Replace

1. **Replace the placeholder files** with your actual Yark branding assets
2. **Keep the same filenames** for automatic integration
3. **Update the code** in main.py if needed (see instructions below)

## Code Integration

### For Main Logo (About Dialog)
The logo is loaded in `main.py` in the `show_about()` function. Update the path:

```python
# Change this line:
logo_image = Image.open("logo.png")
# To this:
logo_image = Image.open("branding/logo.png")
```

### For Window Icon
Add this code in `main.py` in the `__init__` method after `self.root.geometry("1400x900")`:

```python
# Set application icon
try:
    self.root.iconbitmap("branding/icon.ico")  # For .ico files
except:
    try:
        # Fallback to PNG icon
        icon_image = tk.PhotoImage(file="branding/icon.png")
        self.root.iconphoto(True, icon_image)
    except:
        pass  # Continue without icon if files not found
```

## File Specifications

### Logo Requirements
- **Minimum Size**: 100x100 pixels
- **Recommended Size**: 256x256 pixels or larger
- **Format**: PNG with transparent background preferred
- **Content**: Yark company logo/branding

### Icon Requirements
- **ICO Format**: Multiple sizes (16x16, 32x32, 48x48, 64x64)
- **PNG Format**: 64x64 pixels minimum
- **Content**: Simplified version of logo suitable for small sizes

## Current Status
- ✅ Placeholder files created
- ⏳ Waiting for actual Yark branding assets
- ⏳ Code integration pending asset replacement

## Contact
For questions about branding requirements:
🌐 Yark.com
📞 +92 309 2656986
