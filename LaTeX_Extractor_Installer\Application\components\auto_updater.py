#!/usr/bin/env python3
"""
LaTeX Extractor by Yark - Auto Updater
Handles automatic updates for the application
"""

import os
import sys
import requests
import json
import subprocess
import tempfile
import hashlib
from pathlib import Path
import time
import threading
from typing import Optional, Dict, Any
import tkinter as tk
from tkinter import messagebox

class AutoUpdater:
    """Handles application updates"""
    
    def __init__(self):
        self.current_version = "2.0.0"
        self.update_server = "https://yark.com/latex-extractor/updates"
        self.app_dir = Path(__file__).parent.parent
        self.config_dir = Path.home() / "AppData" / "Local" / "LaTeX Extractor"
        self.update_config_file = self.config_dir / "update_config.json"
        
        # Create config directory
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Load update configuration
        self.update_config = self.load_update_config()
    
    def log(self, message, level="INFO"):
        """Log updater activity"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def load_update_config(self):
        """Load update configuration"""
        default_config = {
            "auto_check": True,
            "auto_download": False,
            "auto_install": False,
            "check_frequency": "weekly",  # daily, weekly, monthly
            "last_check": None,
            "skip_version": None,
            "beta_updates": False
        }
        
        try:
            if self.update_config_file.exists():
                with open(self.update_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with defaults
                    default_config.update(config)
        except Exception as e:
            self.log(f"⚠️  Could not load update config: {e}", "WARNING")
        
        return default_config
    
    def save_update_config(self):
        """Save update configuration"""
        try:
            with open(self.update_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.update_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log(f"❌ Failed to save update config: {e}", "ERROR")
    
    def should_check_for_updates(self):
        """Check if it's time to check for updates"""
        if not self.update_config["auto_check"]:
            return False
        
        last_check = self.update_config.get("last_check")
        if not last_check:
            return True
        
        try:
            last_check_time = time.strptime(last_check, "%Y-%m-%d %H:%M:%S")
            last_check_timestamp = time.mktime(last_check_time)
            current_timestamp = time.time()
            
            frequency = self.update_config["check_frequency"]
            intervals = {
                "daily": 24 * 3600,
                "weekly": 7 * 24 * 3600,
                "monthly": 30 * 24 * 3600
            }
            
            interval = intervals.get(frequency, 7 * 24 * 3600)  # Default to weekly
            
            return (current_timestamp - last_check_timestamp) >= interval
            
        except Exception as e:
            self.log(f"⚠️  Error checking update schedule: {e}", "WARNING")
            return True
    
    def check_for_updates(self):
        """Check for available updates"""
        self.log("🔍 Checking for updates...")
        
        try:
            # Update last check time
            self.update_config["last_check"] = time.strftime("%Y-%m-%d %H:%M:%S")
            self.save_update_config()
            
            # Request update information
            update_url = f"{self.update_server}/check"
            params = {
                "current_version": self.current_version,
                "platform": "windows",
                "arch": "x64",
                "beta": self.update_config.get("beta_updates", False)
            }
            
            response = requests.get(update_url, params=params, timeout=10)
            response.raise_for_status()
            
            update_info = response.json()
            
            if update_info.get("update_available"):
                self.log(f"✅ Update available: {update_info['latest_version']}")
                return update_info
            else:
                self.log("✅ Application is up to date")
                return None
                
        except requests.RequestException as e:
            self.log(f"⚠️  Could not check for updates: {e}", "WARNING")
            return None
        except Exception as e:
            self.log(f"❌ Update check error: {e}", "ERROR")
            return None
    
    def download_update(self, update_info):
        """Download update package"""
        self.log(f"📦 Downloading update {update_info['latest_version']}...")
        
        try:
            download_url = update_info["download_url"]
            file_size = update_info.get("file_size", 0)
            checksum = update_info.get("checksum")
            
            # Create temporary download location
            temp_dir = Path(tempfile.gettempdir()) / "latex_extractor_update"
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            update_file = temp_dir / f"LaTeX_Extractor_Update_{update_info['latest_version']}.exe"
            
            # Download with progress
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            downloaded = 0
            with open(update_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if file_size > 0:
                            progress = int((downloaded / file_size) * 100)
                            print(f"\rDownload progress: {progress}%", end="", flush=True)
            
            print()  # New line after progress
            
            # Verify checksum if provided
            if checksum:
                if not self.verify_checksum(update_file, checksum):
                    self.log("❌ Update file checksum verification failed", "ERROR")
                    return None
            
            self.log(f"✅ Update downloaded: {update_file}")
            return update_file
            
        except Exception as e:
            self.log(f"❌ Update download failed: {e}", "ERROR")
            return None
    
    def verify_checksum(self, file_path, expected_checksum):
        """Verify file checksum"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_checksum = sha256_hash.hexdigest()
            return actual_checksum.lower() == expected_checksum.lower()
            
        except Exception as e:
            self.log(f"❌ Checksum verification error: {e}", "ERROR")
            return False
    
    def install_update(self, update_file):
        """Install downloaded update"""
        self.log(f"🔧 Installing update from {update_file}...")
        
        try:
            # Run the update installer
            # The installer should handle closing the current application
            subprocess.Popen([str(update_file), '/S'], shell=True)
            
            self.log("✅ Update installation initiated")
            return True
            
        except Exception as e:
            self.log(f"❌ Update installation failed: {e}", "ERROR")
            return False
    
    def show_update_dialog(self, update_info):
        """Show update notification dialog"""
        try:
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            message = f"""A new version of LaTeX Extractor by Yark is available!

Current Version: {self.current_version}
Latest Version: {update_info['latest_version']}

Release Notes:
{update_info.get('release_notes', 'No release notes available.')}

Would you like to download and install the update now?"""
            
            result = messagebox.askyesnocancel(
                "Update Available",
                message,
                icon=messagebox.INFO
            )
            
            root.destroy()
            
            if result is True:
                return "install"
            elif result is False:
                return "skip"
            else:
                return "later"
                
        except Exception as e:
            self.log(f"⚠️  Could not show update dialog: {e}", "WARNING")
            return "later"
    
    def handle_update_notification(self, update_info):
        """Handle update notification based on user preferences"""
        if self.update_config.get("auto_install"):
            # Auto install
            update_file = self.download_update(update_info)
            if update_file:
                return self.install_update(update_file)
        
        elif self.update_config.get("auto_download"):
            # Auto download, ask to install
            update_file = self.download_update(update_info)
            if update_file:
                choice = self.show_update_dialog(update_info)
                if choice == "install":
                    return self.install_update(update_file)
                elif choice == "skip":
                    self.update_config["skip_version"] = update_info['latest_version']
                    self.save_update_config()
        
        else:
            # Ask user
            choice = self.show_update_dialog(update_info)
            if choice == "install":
                update_file = self.download_update(update_info)
                if update_file:
                    return self.install_update(update_file)
            elif choice == "skip":
                self.update_config["skip_version"] = update_info['latest_version']
                self.save_update_config()
        
        return False
    
    def check_and_update(self):
        """Check for updates and handle them according to user preferences"""
        if not self.should_check_for_updates():
            return False
        
        update_info = self.check_for_updates()
        if not update_info:
            return False
        
        # Check if user wants to skip this version
        if update_info['latest_version'] == self.update_config.get("skip_version"):
            self.log(f"Skipping version {update_info['latest_version']} as requested")
            return False
        
        return self.handle_update_notification(update_info)
    
    def check_updates_background(self):
        """Check for updates in background thread"""
        def update_thread():
            try:
                self.check_and_update()
            except Exception as e:
                self.log(f"❌ Background update check failed: {e}", "ERROR")
        
        thread = threading.Thread(target=update_thread, daemon=True)
        thread.start()
        return thread
    
    def get_update_settings(self):
        """Get current update settings"""
        return self.update_config.copy()
    
    def update_settings(self, new_settings):
        """Update settings"""
        self.update_config.update(new_settings)
        self.save_update_config()

def main():
    """Test the auto updater"""
    print("LaTeX Extractor by Yark - Auto Updater Test")
    print("=" * 50)
    
    updater = AutoUpdater()
    
    print(f"📋 Current Version: {updater.current_version}")
    print(f"🔧 Update Server: {updater.update_server}")
    
    # Show current settings
    settings = updater.get_update_settings()
    print("\n⚙️  Update Settings:")
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    # Check if should check for updates
    should_check = updater.should_check_for_updates()
    print(f"\n🕒 Should check for updates: {should_check}")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        print("\n🔍 Checking for updates...")
        update_info = updater.check_for_updates()
        if update_info:
            print(f"✅ Update available: {update_info}")
        else:
            print("✅ No updates available")

if __name__ == "__main__":
    main()
