@echo off
title LaTeX Extractor by Yark - Installer

echo ================================================================
echo LaTeX Extractor by Yark v2.0.0 - Installer
echo Mathematical Equation Processor
echo ================================================================
echo.
echo 🚀 Welcome to LaTeX Extractor by Yark Installation
echo.
echo This installer will:
echo • Install Python 3.13.3 (if not present)
echo • Install Tesseract OCR
echo • Install Ollama (AI Engine)
echo • Install all Python dependencies offline
echo • Install LaTeX Extractor application
echo.

set /p CONFIRM="Do you want to continue? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo 📦 Starting installation process...

REM Get installation directory
set INSTALL_DIR=%PROGRAMFILES%\LaTeX Extractor by Yark
echo Installation directory: %INSTALL_DIR%

REM Create installation directory
mkdir "%INSTALL_DIR%" 2>nul

REM Install Python if not present
echo.
echo 🐍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo Installing Python 3.13.3...
    "%~dp0..\Dependencies\python-3.13.3-amd64.exe" /quiet InstallAllUsers=1 PrependPath=1 Include_pip=1
    if errorlevel 1 (
        echo ❌ Python installation failed
        pause
        exit /b 1
    )
    echo ✅ Python installed successfully
) else (
    echo ✅ Python already installed
)

REM Install Tesseract OCR
echo.
echo 👁️ Installing Tesseract OCR...
"%~dp0..\Dependencies\tesseract-ocr-w64-setup-5.5.0.20241111.exe" /S
echo ✅ Tesseract OCR installed

REM Install Ollama
echo.
echo 🤖 Installing Ollama AI Engine...
"%~dp0..\Dependencies\OllamaSetup.exe" /S
echo ✅ Ollama installed

REM Install Python packages from offline wheels
echo.
echo 📦 Installing Python packages...
call "%~dp0..\Dependencies\install_packages.bat"

REM Copy application files
echo.
echo 📁 Installing LaTeX Extractor application...
xcopy "%~dp0..\Application\*" "%INSTALL_DIR%\" /E /I /Y
echo ✅ Application files copied

REM Create desktop shortcut
echo.
echo 🔗 Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\LaTeX Extractor by Yark.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\LaTeX_Extractor.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

REM Create start menu shortcut
mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\LaTeX Extractor by Yark" 2>nul
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\LaTeX Extractor by Yark\LaTeX Extractor by Yark.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\LaTeX_Extractor.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo ✅ Installation completed successfully!
echo.
echo 📁 Installation directory: %INSTALL_DIR%
echo 🖥️  Desktop shortcut created
echo 📋 Start menu shortcut created
echo.
echo 🎉 LaTeX Extractor by Yark is ready to use!
echo.
echo 📞 Support: +92 309 2656986
echo 🌐 Website: https://yark.com
echo.
pause
