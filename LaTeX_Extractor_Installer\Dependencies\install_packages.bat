@echo off
title LaTeX Extractor - Installing Dependencies...

echo ================================================================
echo LaTeX Extractor by Yark - Dependency Installation
echo ================================================================
echo.
echo [INSTALL] Installing Python packages from offline cache...

REM Get the directory where this script is located
set SCRIPT_DIR=%~dp0
set WHEELS_DIR=%SCRIPT_DIR%wheels

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python first
    pause
    exit /b 1
)

echo [SUCCESS] Python found
echo.

REM Install packages from wheels directory
echo [INSTALL] Installing core packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" Pillow opencv-python numpy
if errorlevel 1 goto :error

echo [INSTALL] Installing PDF processing packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" PyMuPDF pdf2image
if errorlevel 1 goto :error

echo [INSTALL] Installing OCR packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" pytesseract pix2tex
if errorlevel 1 goto :error

echo [INSTALL] Installing AI/ML packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" torch torchvision transformers accelerate protobuf
if errorlevel 1 goto :error

echo [INSTALL] Installing document processing packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" python-docx lxml
if errorlevel 1 goto :error

echo [INSTALL] Installing utility packages...
python -m pip install --no-index --find-links "%WHEELS_DIR%" requests psutil typing-extensions
if errorlevel 1 goto :error

echo.
echo [SUCCESS] All packages installed successfully!
echo [COMPLETE] LaTeX Extractor dependencies are ready
echo.
pause
exit /b 0

:error
echo.
echo [ERROR] Package installation failed
echo Please check the error messages above
echo.
echo Support: +92 309 2656986
echo Website: https://yark.com
pause
exit /b 1
